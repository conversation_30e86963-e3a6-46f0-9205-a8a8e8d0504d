.shimmer-item {
  position: relative;
  overflow: hidden;
}

.shimmer-rectangle {
  border-radius: 4px;
}

.shimmer-circle {
  border-radius: 50%;
}

.shimmer-rounded {
  border-radius: 8px;
}

.shimmer-square {
  border-radius: 0;
}

.shimmer-animation-wave {
  background: linear-gradient(
    90deg,
    rgba(255, 255, 255, 0) 0%,
    rgba(255, 255, 255, 0.5) 50%,
    rgba(255, 255, 255, 0) 100%
  );
  background-size: 200% 100%;
  animation: shimmer-wave 1.5s infinite;
}

.shimmer-animation-pulse {
  animation: shimmer-pulse 1.5s infinite ease-in-out;
}

@keyframes shimmer-wave {
  0% {
    background-position: 200% 0; 
  }
  100% {
    background-position: -200% 0; 
  }
}

@keyframes shimmer-pulse {
  0% {
    opacity: 0.6;
  }
  50% {
    opacity: 1;
  }
  100% {
    opacity: 0.6;
  }
}
