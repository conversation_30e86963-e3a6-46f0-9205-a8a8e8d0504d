.table-wrapper {
  position: relative;
  overflow-x: auto;

  .ava-user-table {
    width: 100%;
    border-collapse: collapse;
    font-family: var(--table-font-family-body);
    margin: 1rem 0;
    border: 1px solid var(--table-border);
    overflow-x: auto;
    th,
    td {
      padding: 1rem;
      text-align: left;
    }

    thead {
      background-color: var(--table-header-background-color);
    }

    tbody tr {
      transition: background-color 0.3s ease;

      &:nth-child(odd) {
        background-color: var(--table-background-color-odd);
      }

      &:nth-child(even) {
        background: var(--table-background-color-even);
      }

      .cell-link {
        color: inherit;
        text-decoration: none;
        cursor: pointer;
      }

      .cell-link:hover {
        text-decoration: underline;
      }
    }

    // Style for action icons
    .ava-icon {
      margin: 2px;
      padding: 2px;
    }

    [data-theme="dark"].dynamic-icon,
    .ava-icon {
      color: var(--table-background-color-odd);
    }

    [data-theme="dark"].dynamic-icon:hover,
    .ava-icon:hover {
      color: var(--table-background-color-odd);
    }
  }
  .dropdown {
    position: relative;

    [data-theme="dark"] .action-button {
      color: var(--table-background-color-odd);

      &:hover {
        color: var(--table-background-color-odd);
      }
    }
    .dropdown-menu {
      position: absolute;
      top: 100%;
      right: 0;
      margin-top: 0.5rem;
      min-width: 13.75rem;
      padding: 0.25rem 0;
      background: var(--table-background-color-odd);
      border: 1px solid var(--table-border);
      border-radius: 0.5rem;
      box-shadow: 0 0.25rem 0.625rem rgba(0, 0, 0, 0.08);
      z-index: 1000;

      [data-theme="dark"] .dropdown-menu {
        background: var(--table-background-color-odd);
        border: 1px solid var(--table-border);
        color: var(--table-background-color-odd);
      }

      .dropdown-item {
        display: flex;
        align-items: center;
        gap: 0.625rem;
        width: 100%;
        padding: 0.625rem 1rem;
        font-size: 1rem;
        text-align: left;
        background: none;
        border: none;
        cursor: pointer;
        white-space: nowrap;
        color: var(--text-color);

        &:hover {
          background: var(--table-background-color-odd);
        }

        .dropdown-icon {
          font-size: 1rem;
        }
      }
    }
  }

  .actions-cell {
    width: 1%;
    white-space: nowrap;
    text-align: right;

    .action-button {
      background: none;
      border: none;
      font-size: 1.25rem;
      cursor: pointer;
      padding: 0;

      &:hover {
        background-color: rgba(0, 0, 0, 0.05);
      }

      .ava-icon {
        color: inherit;
      }
    }
  }

  .actions-wrapper {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    position: relative;

    .action-button,
    .action-icon {
      background: none;
      border: none;
      font-size: 1.125rem;
      cursor: pointer;
      padding: 0.25rem;
      border-radius: 0.375rem;

      &:hover {
        background-color: rgba(0, 0, 0, 0.05);
      }

      .ava-icon {
        color: inherit;
      }
    }
  }

  th.sortable:hover .header-label::after {
    content: "";
    display: inline-block;
    width: 1rem;
    height: 1rem;
    margin-left: 0.25rem;
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='none' stroke='%23616161' stroke-width='2' stroke-linecap='round' stroke-linejoin='round' viewBox='0 0 24 24'%3E%3Cpolyline points='6 9 12 15 18 9'/%3E%3C/svg%3E");
    background-size: 1rem;
    background-repeat: no-repeat;
    opacity: 0.6;
  }

  th.sorted-asc .header-label::after {
    transform: rotate(180deg);
  }
}
