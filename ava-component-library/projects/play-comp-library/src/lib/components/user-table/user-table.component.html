<div class="user-table-wrapper" #tableRef>
  <div class="table-wrapper">
    <table class="ava-user-table">
      <thead>
        <tr
          (dragover)="columnDrag ? onColumnDragOver($event) : null"
          (drop)="columnDrag ? onColumnDrop($event) : null"
        >
          <ng-container *ngFor="let col of columns; let colIndex = index">
            <th
              *ngIf="col.visible"
              [attr.draggable]="columnDrag ? true : null"
              (dragstart)="
                columnDrag ? onColumnDragStart($event, colIndex) : null
              "
              (dragenter)="
                columnDrag ? onColumnDragEnter($event, colIndex) : null
              "
              [class.drag-over]="columnDrag && dragOverColIndex === colIndex"
              [class.sortable]="col.sortable"
              [class.filterable]="col.filterable"
              [class.sorted-asc]="
                sortColumn === col.field && sortDirection === 'asc'
              "
              [class.sorted-desc]="
                sortColumn === col.field && sortDirection === 'desc'
              "
              (mouseenter)="hoveredCol = col.field"
              (mouseleave)="hoveredCol = null"
              (click)="onSort(col)"
            >
              <span class="header-label">{{ col.label }}</span>

              <span
                *ngIf="col.filterable"
                class="filter-icon"
                (click)="onFilterIconClick($event, col.field)"
                [class.active]="isFilterActive(col.field)"
                title="Filter"
              ></span>

              <div
                class="resize-handle"
                *ngIf="resizable"
                (mousedown)="onResizeMouseDown($event, colIndex)"
              ></div>
            </th>
          </ng-container>
        </tr>
      </thead>

      <tbody
        (dragover)="rowDrag ? onRowDragOver($event) : null"
        (drop)="rowDrag ? onRowDrop($event) : null"
      >
        <tr
          *ngFor="let row of paginatedData; let rowIndex = index"
          [attr.draggable]="rowDrag ? true : null"
          (dragstart)="rowDrag ? onRowDragStart($event, rowIndex) : null"
          (dragenter)="rowDrag ? onRowDragEnter($event, rowIndex) : null"
          [class.drag-over]="rowDrag && dragOverRowIndex === rowIndex"
        >
          <ng-container *ngFor="let col of columns">
            <td
              *ngIf="col.visible"
              [ngClass]="{ 'actions-cell': col.field === 'actions' }"
            >
              <ng-container *ngIf="col.field !== 'actions'; else actions">
                <ng-container
                  *ngIf="row[col.field]?.clickable === true; else normalText"
                >
                  <a
                    href="javascript:void(0)"
                    (click)="handleCellClick(row, col.field)"
                    class="cell-link"
                  >
                    <ng-container *ngIf="row[col.field]?.iconName">
                      <ng-container
                        *ngIf="
                          isUrl(row[col.field]?.iconName);
                          else renderAvaIcon
                        "
                      >
                        <span
                          class="dynamic-icon"
                          [innerHTML]="getIcon(row[col.field]?.iconName)"
                        ></span>
                      </ng-container>
                      <ng-template #renderAvaIcon>
                        <ava-icon
                          [iconName]="row[col.field]?.iconName"
                          [iconSize]="'16'"
                          [iconColor]="'currentColor'"
                          aria-hidden="true"
                          class="ava-icon"
                        ></ava-icon>
                      </ng-template>
                    </ng-container>
                    <span>{{ getCellValue(row, col.field) }}</span>
                  </a>
                </ng-container>

                <ng-template #normalText>
                  <ng-container *ngIf="row[col.field]?.iconName">
                    <ng-container
                      *ngIf="
                        isUrl(row[col.field]?.iconName);
                        else renderAvaIconText
                      "
                    >
                      <span
                        class="dynamic-icon"
                        [innerHTML]="getIcon(row[col.field]?.iconName)"
                      ></span>
                    </ng-container>
                    <ng-template #renderAvaIconText>
                      <ava-icon
                        [iconName]="row[col.field]?.iconName"
                        [iconSize]="'16'"
                        [iconColor]="'currentColor'"
                        aria-hidden="true"
                        class="ava-icon"
                      ></ava-icon>
                    </ng-template>
                  </ng-container>
                  <span>{{ getCellValue(row, col.field) }}</span>
                </ng-template>
              </ng-container>

              <ng-template #actions>
                <div class="actions-wrapper">
                  <!-- Inline Action Buttons -->
                  <ng-container *ngFor="let entry of getInlineActions(row)">
                    <div
                      class="action-icon"
                      [title]="entry[1].label"
                      (click)="handleAction(row, entry[0])"
                    >
                      <span class="dynamic-icon" *ngIf="entry[1].icon">
                        <ng-container
                          *ngIf="isUrl(entry[1].icon); else renderAvaIcon"
                        >
                          <span
                            [innerHTML]="getActionIconHtml(entry[1].icon)"
                          ></span>
                        </ng-container>
                        <ng-template #renderAvaIcon>
                          <ava-icon
                            [iconName]="entry[1].icon"
                            [iconSize]="'16'"
                            [iconColor]="'currentColor'"
                            aria-hidden="true"
                            class="ava-icon"
                          ></ava-icon>
                        </ng-template>
                      </span>
                    </div>
                  </ng-container>

                  <!-- Dropdown for overflow actions -->
                  <div class="dropdown" *ngIf="shouldShowDropdown(row)">
                    <ava-icon
                      class="ava-icon"
                      iconName="ellipsis-vertical"
                      iconColor="black"
                      [iconSize]="16"
                      [cursor]="true"
                      (click)="toggleDropdown(row)"
                    >
                    </ava-icon>
                    <div
                      class="dropdown-menu"
                      *ngIf="dropdownRow === row"
                      #dropdownMenu
                      (mouseleave)="dropdownRow = null"
                    >
                      <ng-container
                        *ngFor="let entry of getDropdownActions(row)"
                      >
                        <div
                          class="dropdown-item"
                          role="button"
                          tabindex="0"
                          (click)="handleAction(row, entry[0])"
                        >
                          <span class="dropdown-icon" *ngIf="entry[1].icon">
                            <ng-container
                              *ngIf="isUrl(entry[1].icon); else renderAvaIcon"
                            >
                              <span
                                [innerHTML]="getActionIconHtml(entry[1].icon)"
                              ></span>
                            </ng-container>
                            <ng-template #renderAvaIcon>
                              <ava-icon
                                [iconName]="entry[1].icon"
                                [iconSize]="'16'"
                                [iconColor]="'currentColor'"
                                aria-hidden="true"
                                class="ava-icon"
                              ></ava-icon>
                            </ng-template>
                          </span>
                          {{ entry[1].label }}
                        </div>
                      </ng-container>
                    </div>
                  </div>
                </div>
              </ng-template>
            </td>
          </ng-container>
        </tr>

        <tr *ngIf="paginatedData.length === 0">
          <td [attr.colspan]="columns.length" class="no-records-cell"></td>
        </tr>
      </tbody>
    </table>
  </div>

  <!-- Default Filter Modal rendered outside table -->
  <!-- <div
    class="default-filter-modal"
    *ngIf="useDefaultFilter && openFilterField"
    [style.left.px]="filterDropdownPosition?.x"
    [style.top.px]="filterDropdownPosition?.y"
  >
    <select
      [(ngModel)]="defaultColumnFilters[openFilterField!].type"
      class="default-filter-select"
    >
      <option *ngFor="let opt of defaultFilterConditions">{{ opt }}</option>
    </select>

    <input
      *ngIf="!isFilterTypeEmpty(defaultColumnFilters[openFilterField!].type)"
      [(ngModel)]="defaultColumnFilters[openFilterField!].value"
      type="text"
      class="default-filter-input"
      placeholder="Enter value"
    />

    <div class="default-filter-actions">
      <ava-button
        label="Filter"
        variant="primary"
        (userClick)="applyAndCloseFilter()"
        pressedEffect="ripple"
      ></ava-button>
      <ava-button
        label="Clear"
        variant="secondary"
        (userClick)="clearDefaultFilter()"
        pressedEffect="ripple"
      ></ava-button>
    </div>
  </div> -->
</div>
