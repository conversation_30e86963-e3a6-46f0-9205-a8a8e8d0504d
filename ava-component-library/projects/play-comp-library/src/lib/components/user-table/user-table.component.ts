import {
  ChangeDetectionStrategy,
  Component,
  ElementRef,
  EventEmitter,
  HostListener,
  Input,
  OnInit,
  Output,
  ViewChild,
} from '@angular/core';
import {
  ActionConfig,
  ColumnConfig,
  User,
} from '../../../../../playground/src/app/components/app-table/app-table.component';
import { CommonModule } from '@angular/common';
import { DomSanitizer, SafeHtml } from '@angular/platform-browser';
import { LucideAngularModule } from 'lucide-angular';
import { FormsModule } from '@angular/forms';
import { IconComponent } from '../icon/icon.component';
import { ButtonComponent } from '../button/button.component';

@Component({
  selector: 'ava-user-table',
  imports: [
    CommonModule,
    FormsModule,
    LucideAngularModule,
    IconComponent,
    ButtonComponent,
  ],
  templateUrl: './user-table.component.html',
  styleUrl: './user-table.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class UserTableComponent implements OnInit {
  @Input() columns: ColumnConfig[] = [];
  @Input() data: User[] = [];
  @Input() rowDrag = false;
  @Input() columnDrag = false;
  @Input() useDefaultFilter = false;
  @Input() resizable = false;
  @ViewChild('tableRef', { static: false }) tableRef?: ElementRef;
  paginatedData: any[] = [];
  //sorting
  hoveredCol: string | null = null;
  sortDirection: 'asc' | 'desc' | '' = '';
  sortColumn: keyof User | '' = '';
  dropdownRow: User | null = null;
  //originalData: User[] = [];
  filteredData: User[] = [];
  //filter
  filterDropdownPosition: { x: number; y: number } | null = null;
  tempSelectedFilters: { [key: string]: Set<string> } = {};
  columnFilters: { [key: string]: string[] } = {};
  activeFilters: { [key: string]: Set<string> } = {};
  openFilterField: string | null = null;
  defaultColumnFilters: {
    [field: string]: {
      value: string;
      type: string;
    };
  } = {};
  defaultFilterConditions = [
    'Starts With',
    'Ends With',
    'Contains',
    'Equal',
    'Empty',
    'Does Not Start With',
    'Does Not End With',
    'Does Not Contain',
    'Not Equal',
    'Not Empty',
  ];
  //drag and drop
  page = 0;
  rowsPerPage = 10;
  dragStartRowIndex: number | null = null;
  dragOverRowIndex: number | null = null;
  dragStartColIndex: number | null = null;
  dragOverColIndex: number | null = null;

  //cell click
  @Output() cellClick = new EventEmitter<{ row: User; field: string }>();
  @Output() actionTriggered = new EventEmitter<{
    row: User;
    actionKey: string;
    config: ActionConfig;
  }>();

  constructor(private sanitizer: DomSanitizer) {}
  ngOnInit() {
    this.paginatedData = this.data;
    this.applyDefaultFilter();
  }
  //Sorting
  onSort(col: ColumnConfig): void {
    if (!col.sortable || col.field === 'actions') return;
    this.sortColumn = col.field as keyof User;
    this.sortDirection = this.sortDirection === 'asc' ? 'desc' : 'asc';
    this.applySortingAndPagination();
  }
  applySortingAndPagination(): void {
    const sortedData = [...this.data];
    if (this.sortDirection && this.sortColumn) {
      const key = this.sortColumn;
      sortedData.sort((a, b) => {
        const aVal = this.getFieldValue(a[key]);
        const bVal = this.getFieldValue(b[key]);
        if (aVal == null) return 1;
        if (bVal == null) return -1;
        return this.sortDirection === 'asc'
          ? String(aVal).localeCompare(String(bVal))
          : String(bVal).localeCompare(String(aVal));
      });
    }
    const start = this.page * this.rowsPerPage;
    const end = start + this.rowsPerPage;
    this.paginatedData = sortedData.slice(start, end);
  }

  getFieldValue(field: any): string {
    if (field && typeof field === 'object' && 'value' in field) {
      return String(field.value);
    }
    return String(field ?? '');
  }

  //Start Filtering

  applyDefaultFilter(): void {
    if (!this.openFilterField) return;

    const field = this.openFilterField as keyof User;
    const { type, value } = this.defaultColumnFilters[field];
    const val = value.toLowerCase().trim();

    this.filteredData = this.data.filter((row) => {
      const rawCell = row[field];

      // Type guard to ensure rawCell is FieldWithIcon
      if (
        rawCell &&
        typeof rawCell === 'object' &&
        'value' in rawCell &&
        typeof rawCell.value === 'string'
      ) {
        const cell = rawCell.value.toLowerCase();

        switch (type) {
          case 'Starts With':
            return cell.startsWith(val);
          case 'Ends With':
            return cell.endsWith(val);
          case 'Contains':
            return cell.includes(val);
          case 'Equal':
            return cell === val;
          case 'Empty':
            return cell === '';
          case 'Does Not Start With':
            return !cell.startsWith(val);
          case 'Does Not End With':
            return !cell.endsWith(val);
          case 'Does Not Contain':
            return !cell.includes(val);
          case 'Not Equal':
            return cell !== val;
          case 'Not Empty':
            return cell !== '';
          default:
            return true;
        }
      }

      return false;
    });

    this.page = 0;
    this.paginatedData = this.filteredData.slice(
      this.page * this.rowsPerPage,
      (this.page + 1) * this.rowsPerPage
    );
  }

  applyAndCloseFilter(): void {
    this.applyDefaultFilter();
    this.openFilterField = null;
    this.filterDropdownPosition = null;
  }

  isFilterTypeEmpty(type: string): boolean {
    return type === 'empty' || type === 'not empty';
  }

  clearDefaultFilter(): void {
    if (!this.openFilterField) return;

    // Reset this column's filter values
    this.defaultColumnFilters[this.openFilterField] = {
      value: '',
      type: 'Starts With',
    };
    // Reset full data
    this.filteredData = [...this.data];
    // Close modal
    this.openFilterField = null;
    // Refresh view
    this.page = 0;
    this.applySortingAndPagination();
  }

  onFilterIconClick(event: MouseEvent, field: string): void {
    event.stopPropagation();

    const iconRect = (event.target as HTMLElement).getBoundingClientRect();
    const tableRect = this.tableRef?.nativeElement.getBoundingClientRect();

    if (!tableRect) return;

    const modalWidth = 250;
    const screenWidth = window.innerWidth;

    let left = iconRect.left - tableRect.left;
    if (left + modalWidth > screenWidth) {
      left = screenWidth - modalWidth - 12 - tableRect.left;
    }

    this.filterDropdownPosition = {
      x: left,
      y: iconRect.bottom - tableRect.top + 6,
    };

    this.openFilterField = this.openFilterField === field ? null : field;

    if (!this.defaultColumnFilters[field]) {
      this.defaultColumnFilters[field] = {
        value: '',
        type: 'Starts With',
      };
    }
    this.tempSelectedFilters[field] = new Set(this.activeFilters[field]);
  }

  isFilterActive(field: string): boolean {
    const filter = this.defaultColumnFilters[field];
    return !!(filter && filter.value.trim().length > 0);
  }
  //End Filtering

  // Start column resizing
  private resizing = {
    active: false,
    startX: 0,
    startWidth: 0,
    colIndex: -1,
  };
  onMouseMove = (event: MouseEvent) => {
    if (!this.resizing.active) return;

    const deltaX = event.pageX - this.resizing.startX;
    const ths = this.tableRef?.nativeElement.querySelectorAll('th');
    if (!ths || !ths[this.resizing.colIndex]) return;

    const th = ths[this.resizing.colIndex] as HTMLElement;
    th.style.width = `${this.resizing.startWidth + deltaX}px`;
  };

  onMouseUp = () => {
    if (!this.resizing.active) return;

    this.resizing.active = false;
    document.removeEventListener('mousemove', this.onMouseMove);
    document.removeEventListener('mouseup', this.onMouseUp);
  };

  onResizeMouseDown(event: MouseEvent, colIndex: number) {
    if (!this.resizable) return;
    event.preventDefault();
    this.resizing.active = true;
    this.resizing.startX = event.pageX;
    this.resizing.colIndex = colIndex;

    const th = (event.target as HTMLElement).closest('th')!;
    this.resizing.startWidth = th.offsetWidth;

    document.addEventListener('mousemove', this.onMouseMove);
    document.addEventListener('mouseup', this.onMouseUp);
  }
  // End column resizing

  //Start COLUMN DRAG EVENTS
  onColumnDragStart(event: DragEvent, colIndex: number) {
    this.dragStartColIndex = colIndex;
  }

  onColumnDragEnter(event: DragEvent, colIndex: number) {
    event.preventDefault();
    this.dragOverColIndex = colIndex;
  }

  onColumnDragOver(event: DragEvent) {
    event.preventDefault();
  }

  onRowDragStart(event: DragEvent, rowIndex: number) {
    this.dragStartRowIndex = rowIndex;
  }

  onRowDragEnter(event: DragEvent, rowIndex: number) {
    event.preventDefault();
    this.dragOverRowIndex = rowIndex;
  }

  onRowDragOver(event: DragEvent) {
    event.preventDefault();
  }

  onRowDrop(event: DragEvent) {
    event.preventDefault();
    if (this.dragStartRowIndex === null || this.dragOverRowIndex === null)
      return;

    const start = this.page * this.rowsPerPage;
    const end = start + this.rowsPerPage;

    const dataSlice = [...this.paginatedData];
    const draggedItem = dataSlice.splice(this.dragStartRowIndex, 1)[0];
    dataSlice.splice(this.dragOverRowIndex, 0, draggedItem);

    // Update the main data array accordingly
    this.data.splice(start, this.rowsPerPage, ...dataSlice);

    this.applySortingAndPagination(); // Refresh paginatedData
    this.dragStartRowIndex = this.dragOverRowIndex = null;
  }

  onColumnDrop(event: DragEvent) {
    event.preventDefault();
    if (this.dragStartColIndex === null || this.dragOverColIndex === null)
      return;

    const colA = this.columns[this.dragStartColIndex];
    const colB = this.columns[this.dragOverColIndex];

    [
      this.columns[this.dragStartColIndex],
      this.columns[this.dragOverColIndex],
    ] = [colB, colA];

    this.dragStartColIndex = this.dragOverColIndex = null;
  }
  //End COLUMN DRAG EVENTS

  //Start Icon
  // getIcon(html: string | undefined): SafeHtml {
  //   return html ? this.sanitizer.bypassSecurityTrustHtml(html) : '';
  // }
  getIcon(url: string | undefined): SafeHtml {
    if (!url) return '';
    const imgHTML = `<img src="${url}" width="16" height="16" style="margin-right: 4px; vertical-align: middle;" />`;
    return this.sanitizer.bypassSecurityTrustHtml(imgHTML);
  }

  getActionIconHtml(iconUrl: string | undefined): SafeHtml {
    if (!iconUrl) return '';
    const html = `<img src="${iconUrl}" width="16" height="16" style="vertical-align: middle;" />`;
    return this.sanitizer.bypassSecurityTrustHtml(html);
  }

  // End Icon

  //Table cell Click
  handleCellClick(row: User, field: string) {
    console.log('Cell clicked:', row, field);
    this.cellClick.emit({ row, field });
  }

  getCellValue(row: any, field: string): string {
    if (field === 'action') return '';
    return row[field]?.value || '';
  }

  getInlineActions(row: User): [string, ActionConfig][] {
    return Object.entries(row.action || {}).filter(
      ([, config]) => config.enabled && config.inline
    );
  }
  handleAction(row: User, actionKey: string): void {
    const config = row.action[actionKey];
    console.log(`Action triggered: ${config.label}`, row);
    this.dropdownRow = null;
    this.actionTriggered.emit({ row, actionKey, config });
  }

  @HostListener('document:click', ['$event'])
  onClickOutside(event: MouseEvent): void {
    const clickedInside = this.tableRef?.nativeElement.contains(event.target);
    if (!clickedInside) {
      this.dropdownRow = null;
      this.openFilterField = null;
      this.filterDropdownPosition = null;
    }
  }

  @HostListener('document:keydown.escape', ['$event'])
  onEscape(): void {
    this.dropdownRow = null;
  }

  shouldShowDropdown(row: User): boolean {
    return this.getDropdownActions(row).length > 0;
  }

  getDropdownActions(row: User): [string, ActionConfig][] {
    return Object.entries(row.action || {}).filter(
      ([, config]) => config.enabled && !config.inline
    );
  }

  toggleDropdown(row: User): void {
    this.dropdownRow = this.dropdownRow === row ? null : row;
  }

  isUrl(icon: string): boolean {
    return icon?.startsWith('http://') || icon?.startsWith('https://');
  }
}
