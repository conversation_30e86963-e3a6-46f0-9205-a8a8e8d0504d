<div class="time-picker-container">
  <div class="time-picker-input">
    <div class="time-display" *ngIf="!isFocused" (click)="onDisplayClick($event)">
      {{ displayTime }}
    </div>

    <!-- Input fields interface -->
    <div class="time-input-container" *ngIf="isFocused && showInputMode">
      <input
        type="text"
        class="time-input hours-input"
        [value]="hours"
        (input)="onHoursInput($event)"
        (keydown)="onHoursKeyPress($event)"
        (focus)="onInputFocus($event)"
        (blur)="onInputBlur($event)"
        placeholder="HH"
        maxlength="2">

      <span class="separator">:</span>

      <input
        type="text"
        class="time-input minutes-input"
        [value]="minutes"
        (input)="onMinutesInput($event)"
        (keydown)="onMinutesKeyPress($event)"
        (focus)="onInputFocus($event)"
        (blur)="onInputBlur($event)"
        placeholder="MM"
        maxlength="2">

      <input
        type="text"
        class="time-input period-input"
        [value]="period"
        (input)="onPeriodInput($event)"
        (keydown)="onPeriodKeyPress($event)"
        (focus)="onInputFocus($event)"
        (blur)="onInputBlur($event)"
        placeholder="AM"
        maxlength="2">
    </div>

    <!-- Scrolling time picker interface -->
    <div class="time-scroll-container" *ngIf="isFocused && !showInputMode">
      <!-- Hours scroll -->
      <div class="time-scroll-column">
        <div class="scroll-area" #hoursScroll (scroll)="onScrollEvent($event, 'hours')">
          <div
            *ngFor="let hour of hoursList"
            class="time-item"
            [class.selected]="hour === centeredHour && hour !== ''"
            [class.padding-item]="hour === ''"
            (click)="hour !== '' ? onTimeItemClick($event, 'hours', hour) : null"
            [style.cursor]="hour !== '' ? 'pointer' : 'default'">
            {{ hour }}
          </div>
        </div>
      </div>

      <span class="separator">:</span>

      <!-- Minutes scroll -->
      <div class="time-scroll-column">
        <div class="scroll-area" #minutesScroll (scroll)="onScrollEvent($event, 'minutes')">
          <div
            *ngFor="let minute of minutesList"
            class="time-item"
            [class.selected]="minute === centeredMinute && minute !== ''"
            [class.padding-item]="minute === ''"
            (click)="minute !== '' ? onTimeItemClick($event, 'minutes', minute) : null"
            [style.cursor]="minute !== '' ? 'pointer' : 'default'">
            {{ minute }}
          </div>
        </div>
      </div>

      <!-- Period scroll -->
      <div class="time-scroll-column period-column">
        <div class="scroll-area" #periodScroll (scroll)="onScrollEvent($event, 'period')">
          <div
            *ngFor="let p of periodList"
            class="time-item"
            [class.selected]="p === centeredPeriod && p !== ''"
            [class.padding-item]="p === ''"
            (click)="p !== '' ? onTimeItemClick($event, 'period', p) : null"
            [style.cursor]="p !== '' ? 'pointer' : 'default'">
            {{ p }}
          </div>
        </div>
      </div>
    </div>

    <div class="icon-wrapper" (click)="onIconClick($event)" tabindex="0">
      <ava-icon [iconName]="'clock'" [iconColor]="'gray'" [iconSize]="20"></ava-icon>
    </div>
  </div>
</div>