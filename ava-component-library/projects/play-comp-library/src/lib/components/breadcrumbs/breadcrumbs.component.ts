import { CommonModule } from '@angular/common';
import { ChangeDetectionStrategy, Component, Input, ViewEncapsulation } from '@angular/core';
import { RouterModule } from '@angular/router';
import { IconComponent } from '../icon/icon.component';

@Component({
  selector: 'ava-breadcrumbs',
  imports: [CommonModule, RouterModule, IconComponent],
  templateUrl: './breadcrumbs.component.html',
  styleUrl: './breadcrumbs.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
  encapsulation: ViewEncapsulation.None
})
export class BreadcrumbsComponent {
  @Input() breadcrumbs: { label: string, url: string, active: boolean }[] = [];
  @Input() sizeClass: 'small' | 'medium' | 'large' = 'medium';

  clickedBreadcrumbIndex: number | null = null;

  onBreadcrumbClick(index: number): void {
    this.clickedBreadcrumbIndex = index;
  }

  onBreadcrumbKeydown(event: KeyboardEvent, index: number): void {
    if (event.key === 'ArrowRight') {
      if (index < this.breadcrumbs.length - 1) {
        this.clickedBreadcrumbIndex = index + 1;
      }
    } else if (event.key === 'ArrowLeft') {
      if (index > 0) {
        this.clickedBreadcrumbIndex = index - 1;
      }
    } else if (event.key === 'Enter' || event.key === ' ') {
      this.onBreadcrumbClick(index);
    }
  }

  get displayedBreadcrumbs() {
    if (this.clickedBreadcrumbIndex !== null) {
      return this.breadcrumbs.slice(0, this.clickedBreadcrumbIndex + 1);
    }
    return this.breadcrumbs;
  }
}
