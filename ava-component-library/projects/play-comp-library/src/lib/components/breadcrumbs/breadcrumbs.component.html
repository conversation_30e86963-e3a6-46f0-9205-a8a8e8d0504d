<nav *ngIf="displayedBreadcrumbs.length">
  <ul class="breadcrumb" [ngClass]="sizeClass">
    <li *ngFor="let breadcrumb of displayedBreadcrumbs; let last = last; let i = index"
        [ngClass]="{
            'active': breadcrumb.active,
            'inactive': !breadcrumb.active,
            'clicked': clickedBreadcrumbIndex === i
        }">

      <!-- Breadcrumb link (not last item) -->
      <a *ngIf="!last" [routerLink]="breadcrumb.url"
         (click)="onBreadcrumbClick(i)"
         (keydown)="onBreadcrumbKeydown($event, i)"
         [attr.tabindex]="0"
         [attr.aria-current]="breadcrumb.active ? 'page' : null">
        {{ breadcrumb.label }}
      </a>

      <!-- Current breadcrumb (last item) -->
      <span *ngIf="last">{{ breadcrumb.label }}</span>

      <!-- Separator icon (not after last item) -->
      <ava-icon *ngIf="!last"
                [iconName]="'chevron-right'"
                [iconSize]="14"
                [cursor]="false">
      </ava-icon>

    </li>
  </ul>
</nav>
