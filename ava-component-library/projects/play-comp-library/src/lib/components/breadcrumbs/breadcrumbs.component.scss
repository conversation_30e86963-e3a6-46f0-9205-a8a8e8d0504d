/* Breadcrumb container */
.breadcrumb {
  list-style: none;
  padding: 0;
  margin: 0;
  background: var(--breadcrumbs-background);
  font: var(--breadcrumbs-font);
  display: flex;
  align-items: center;
  gap: 0;
  flex-wrap: wrap;
}

/* Breadcrumb items */
.breadcrumb li {
  display: flex;
  align-items: center;
  color: var(--breadcrumbs-item-text);
  font: var(--breadcrumbs-item-font);
}

/* Breadcrumb links */
.breadcrumb li a {
  color: var(--breadcrumbs-item-text);
  text-decoration: none;
  padding: 0;
  border-radius: var(--breadcrumbs-item-border-radius);
  transition: var(--breadcrumbs-item-transition);
}

.breadcrumb li a:hover {
  color: var(--breadcrumbs-item-hover-text);
  background: var(--breadcrumbs-item-hover-background);
}

/* Current breadcrumb (last item) */
.breadcrumb li span {
  color: var(--breadcrumbs-item-current-text);
  font-weight: var(--breadcrumbs-item-current-font-weight);
  background: var(--breadcrumbs-item-current-background);
  padding: 0;
}

/* Size variants */
.breadcrumb.small {
  font: var(--breadcrumbs-size-sm-font);
}

.breadcrumb.medium {
  font: var(--breadcrumbs-size-md-font);
}

.breadcrumb.large {
  font: var(--breadcrumbs-size-lg-font);
}

/* Size classes */
.breadcrumb.small li {
  font-size: var(--breadcrumbs-size-sm-font);
}

.breadcrumb.small li a,
.breadcrumb.small li span {
  padding: var(--breadcrumbs-size-sm-padding);
}

.breadcrumb.medium li {
  font-size: var(--breadcrumbs-size-md-font);
}

.breadcrumb.medium li a,
.breadcrumb.medium li span {
  padding: var(--breadcrumbs-size-md-padding);
}

.breadcrumb.large li {
  font-size: var(--breadcrumbs-size-lg-font);
}

.breadcrumb.large li a,
.breadcrumb.large li span {
  padding: var(--breadcrumbs-size-lg-padding);
}

/* Active and clicked states */
.breadcrumb li.active a {
  color: var(--breadcrumbs-item-active-text);
  background: var(--breadcrumbs-item-active-background);
}

.breadcrumb li.clicked a {
  color: var(--breadcrumbs-item-active-text);
  background: var(--breadcrumbs-item-active-background);
}

/* Disabled state */
.breadcrumb li.disabled a {
  color: var(--breadcrumbs-item-disabled-text);
  cursor: var(--breadcrumbs-item-disabled-cursor);
  pointer-events: none;
}

/* Accessibility improvements */
.breadcrumb li a:focus {
  outline: 2px solid var(--color-brand-primary);
  outline-offset: 2px;
}

/* Remove margin from last icon */
.breadcrumb li:last-child ava-icon {
  display: none;
}
