/* ===================================================================
   AVA DRAWER COMPONENT STYLES

   Uses Play+ Design System tokens from _base.css and _drawer.css
   All styling references semantic tokens for consistent theming
   =================================================================== */

/* ===================================================================
   SPRING ANIMATION KEYFRAMES
   =================================================================== */
@keyframes slideInRight {
  0% {
    transform: translateX(100%);
    opacity: 0;
  }
  60% {
    transform: translateX(-1%); /* Reduced overshoot to prevent overflow */
    opacity: 1;
  }
  100% {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes slideInLeft {
  0% {
    transform: translateX(-100%);
    opacity: 0;
  }
  60% {
    transform: translateX(1%); /* Reduced overshoot to prevent overflow */
    opacity: 1;
  }
  100% {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes slideInTop {
  0% {
    transform: translateY(-100%);
    opacity: 0;
  }
  60% {
    transform: translateY(1%); /* Reduced overshoot to prevent overflow */
    opacity: 1;
  }
  100% {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes slideInBottom {
  0% {
    transform: translateY(100%);
    opacity: 0;
  }
  60% {
    transform: translateY(-1%); /* Reduced overshoot to prevent overflow */
    opacity: 1;
  }
  100% {
    transform: translateY(0);
    opacity: 1;
  }
}

/* Prevent body scroll when drawer is open */
:global(body.ava-drawer-open) {
  overflow: hidden;
}

/* ===================================================================
   OVERLAY STYLES
   =================================================================== */
.ava-drawer-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: var(--popup-overlay-background, rgba(0, 0, 0, 0.5));
  opacity: 0;
  visibility: hidden;
  transition: opacity var(--global-motion-duration-standard) var(--global-motion-easing-standard),
              visibility var(--global-motion-duration-standard) var(--global-motion-easing-standard);

  &--open {
    opacity: 1;
    visibility: visible;
  }
}

/* ===================================================================
   ANIMATION WRAPPER
   =================================================================== */
.ava-drawer__animation-wrapper {
  width: 100%;
  height: 100%;
  overflow: hidden; /* Prevent animation overflow */

}

/* ===================================================================
   DRAWER BASE STYLES
   =================================================================== */
.ava-drawer {
  position: fixed;
  background: var(--drawer-background);
  border: var(--drawer-border);
  box-shadow: var(--drawer-shadow);
  display: flex;
  flex-direction: column;
  overflow: hidden;
  z-index: var(--drawer-z-index);

  /* ===================================================================
     POSITION VARIANTS
     =================================================================== */
  
  /* Right Position (Default) */
  &--right {
    top: 0;
    right: 0;
    bottom: 0;
    transform: translateX(100%);
    border-left: var(--drawer-border);
    border-right: none;

    &.ava-drawer--open {
      transform: translateX(0);

      /* Animation applied to wrapper */
      .ava-drawer__animation-wrapper.ava-drawer--animated {
        animation: slideInRight var(--drawer-spring-duration) var(--drawer-spring-easing);
      }
    }
  }

  /* Left Position */
  &--left {
    top: 0;
    left: 0;
    bottom: 0;
    transform: translateX(-100%);
    border-right: var(--drawer-border);
    border-left: none;

    &.ava-drawer--open {
      transform: translateX(0);

      /* Animation applied to wrapper */
      .ava-drawer__animation-wrapper.ava-drawer--animated {
        animation: slideInLeft var(--drawer-spring-duration) var(--drawer-spring-easing);
      }
    }
  }

  /* Top Position */
  &--top {
    top: 0;
    left: 0;
    right: 0;
    transform: translateY(-100%);
    border-bottom: var(--drawer-border);
    border-top: none;

    &.ava-drawer--open {
      transform: translateY(0);

      /* Animation applied to wrapper */
      .ava-drawer__animation-wrapper.ava-drawer--animated {
        animation: slideInTop var(--drawer-spring-duration) var(--drawer-spring-easing);
      }
    }
  }

  /* Bottom Position */
  &--bottom {
    bottom: 0;
    left: 0;
    right: 0;
    transform: translateY(100%);
    border-top: var(--drawer-border);
    border-bottom: none;

    &.ava-drawer--open {
      transform: translateY(0);

      /* Animation applied to wrapper */
      .ava-drawer__animation-wrapper.ava-drawer--animated {
        animation: slideInBottom var(--drawer-spring-duration) var(--drawer-spring-easing);
      }
    }
  }

  /* ===================================================================
     SIZE VARIANTS
     =================================================================== */
  
  /* Small Size */
  &--small {
    &.ava-drawer--left,
    &.ava-drawer--right {
      width: 320px;
      max-width: 90vw;
    }

    &.ava-drawer--top,
    &.ava-drawer--bottom {
      height: 200px;
      max-height: 50vh;
    }
  }

  /* Medium Size (Default) */
  &--medium {
    &.ava-drawer--left,
    &.ava-drawer--right {
      width: 480px;
      max-width: 90vw;
    }

    &.ava-drawer--top,
    &.ava-drawer--bottom {
      height: 300px;
      max-height: 60vh;
    }
  }

  /* Large Size */
  &--large {
    &.ava-drawer--left,
    &.ava-drawer--right {
      width: 640px;
      max-width: 90vw;
    }

    &.ava-drawer--top,
    &.ava-drawer--bottom {
      height: 400px;
      max-height: 70vh;
    }
  }

  /* Extra Large Size */
  &--extra-large {
    &.ava-drawer--left,
    &.ava-drawer--right {
      width: 800px;
      max-width: 95vw;
    }

    &.ava-drawer--top,
    &.ava-drawer--bottom {
      height: 500px;
      max-height: 80vh;
    }
  }

  /* Full Size */
  &--full {
    &.ava-drawer--left,
    &.ava-drawer--right {
      width: 100vw;
    }

    &.ava-drawer--top,
    &.ava-drawer--bottom {
      height: 100vh;
    }
  }





  /* No Animation */
  &--none {
    transition: none;
  }
}

/* ===================================================================
   DRAWER CONTENT STRUCTURE
   =================================================================== */
.ava-drawer__content {
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow: hidden;
}

/* ===================================================================
   HEADER STYLES
   =================================================================== */
.ava-drawer__header {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  padding: var(--drawer-header-padding);
  border-bottom: var(--drawer-header-border);
  background: var(--drawer-header-background);
  flex-shrink: 0;
}

.ava-drawer__header-content {
  flex: 1;
  min-width: 0; /* Allows text truncation */
}

.ava-drawer__title-section {
  margin-bottom: var(--global-spacing-2);

  &:last-child {
    margin-bottom: 0;
  }
}

.ava-drawer__title {
  margin: 0;
  font: var(--drawer-title-font);
  color: var(--drawer-title-color);
  font-weight: var(--drawer-title-weight);
  line-height: var(--drawer-title-line-height);
}

.ava-drawer__subtitle {
  margin: var(--drawer-subtitle-margin);
  font: var(--drawer-subtitle-font);
  color: var(--drawer-subtitle-color);
  font-weight: var(--drawer-subtitle-weight);
  line-height: var(--drawer-subtitle-line-height);
}

.ava-drawer__header-slot {
  margin-top: var(--global-spacing-2);
}

.ava-drawer__close-section {
  margin-left: var(--drawer-header-gap);
  flex-shrink: 0;
}



/* ===================================================================
   BODY STYLES
   =================================================================== */
.ava-drawer__body {
  flex: 1;
  overflow-y: auto;
  padding: var(--drawer-body-padding);
  background: var(--drawer-body-background);
  color: var(--drawer-body-text-color);
}

/* ===================================================================
   FOOTER STYLES
   =================================================================== */
.ava-drawer__footer {
  padding: var(--drawer-footer-padding);
  border-top: var(--drawer-footer-border);
  background: var(--drawer-footer-background);
  flex-shrink: 0;
}

/* ===================================================================
   RESIZE HANDLE STYLES
   =================================================================== */
.ava-drawer__resize-handle {
  position: absolute;
  background: transparent;
  z-index: 10;

  &:hover {
    background: var(--color-brand-primary);
    opacity: 0.3;
  }

  /* Right drawer resize handle */
  &--right {
    left: 0;
    top: 0;
    bottom: 0;
    width: 4px;
    cursor: ew-resize;
  }

  /* Left drawer resize handle */
  &--left {
    right: 0;
    top: 0;
    bottom: 0;
    width: 4px;
    cursor: ew-resize;
  }

  /* Top drawer resize handle */
  &--top {
    bottom: 0;
    left: 0;
    right: 0;
    height: 4px;
    cursor: ns-resize;
  }

  /* Bottom drawer resize handle */
  &--bottom {
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    cursor: ns-resize;
  }
}

/* ===================================================================
   RESPONSIVE DESIGN
   =================================================================== */
@media (max-width: 768px) {
  .ava-drawer {
    &--small,
    &--medium,
    &--large,
    &--extra-large {
      &.ava-drawer--left,
      &.ava-drawer--right {
        width: 100vw;
        max-width: 100vw;
      }

      &.ava-drawer--top,
      &.ava-drawer--bottom {
        height: 100vh;
        max-height: 100vh;
      }
    }
  }

  .ava-drawer__header {
    padding: var(--global-spacing-3, 0.75rem);
  }

  .ava-drawer__body {
    padding: var(--global-spacing-3, 0.75rem);
  }

  .ava-drawer__footer {
    padding: var(--global-spacing-3, 0.75rem);
  }
}

/* ===================================================================
   ACCESSIBILITY & REDUCED MOTION
   =================================================================== */
@media (prefers-reduced-motion: reduce) {
  .ava-drawer,
  .ava-drawer-overlay {
    transition: none !important;
  }
}

/* Focus management */
.ava-drawer {
  &:focus {
    outline: none;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .ava-drawer {
    border: 2px solid;
  }

  .ava-drawer__header {
    border-bottom: 2px solid;
  }

  .ava-drawer__footer {
    border-top: 2px solid;
  }
}
