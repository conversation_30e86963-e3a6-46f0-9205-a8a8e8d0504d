/**
 * Component: Breadcrumbs
 * Purpose: Breadcrumbs component tokens for navigation hierarchy
 */

:root {
  /* Breadcrumbs Base */
  --breadcrumbs-background: transparent;
  --breadcrumbs-padding: var(--global-spacing-2);
  --breadcrumbs-font: var(--font-body-2);
  --breadcrumbs-gap: var(--global-spacing-2);

  /* Breadcrumbs Item */
  --breadcrumbs-item-text: var(--color-text-secondary);
  --breadcrumbs-item-font: var(--font-body-2);
  --breadcrumbs-item-padding: var(--global-spacing-1);
  --breadcrumbs-item-border-radius: var(--global-radius-sm);
  --breadcrumbs-item-transition: color var(--global-motion-duration-standard) var(--global-motion-easing-standard);

  /* Breadcrumbs Item States */
  --breadcrumbs-item-hover-text: var(--color-text-primary);
  --breadcrumbs-item-hover-background: var(--color-surface-subtle-hover);

  --breadcrumbs-item-active-text: var(--color-text-primary);
  --breadcrumbs-item-active-background: var(--color-surface-subtle);

  --breadcrumbs-item-current-text: var(--color-text-primary);
  --breadcrumbs-item-current-font-weight: var(--global-font-weight-semibold);
  --breadcrumbs-item-current-background: transparent;

  --breadcrumbs-item-disabled-text: var(--color-text-disabled);
  --breadcrumbs-item-disabled-cursor: not-allowed;

  /* Breadcrumbs Separator */
  --breadcrumbs-separator-text: var(--color-text-disabled);
  --breadcrumbs-separator-font: var(--font-body-2);
  --breadcrumbs-separator-margin: var(--global-spacing-2);
  --breadcrumbs-separator-size: var(--global-icon-size-xs);

  /* Breadcrumbs Icon */
  --breadcrumbs-icon-color: var(--color-text-secondary);
  --breadcrumbs-icon-size: var(--global-icon-size-sm);
  --breadcrumbs-icon-margin: var(--global-spacing-1);

  /* Breadcrumbs Sizes */
  --breadcrumbs-size-sm-font: var(--global-font-size-sm);
  --breadcrumbs-size-sm-padding: var(--global-spacing-1);
  --breadcrumbs-size-sm-gap: var(--global-spacing-1);

  --breadcrumbs-size-md-font: var(--global-font-size-md);
  --breadcrumbs-size-md-padding: var(--global-spacing-2);
  --breadcrumbs-size-md-gap: var(--global-spacing-2);

  --breadcrumbs-size-lg-font: var(--global-font-size-lg);
  --breadcrumbs-size-lg-padding: var(--global-spacing-3);
  --breadcrumbs-size-lg-gap: var(--global-spacing-3);

  /* Breadcrumbs Variants */
  --breadcrumbs-variant-default-background: transparent;
  --breadcrumbs-variant-default-border: none;

  --breadcrumbs-variant-contained-background: var(--color-background-secondary);
  --breadcrumbs-variant-contained-border: 1px solid var(--color-border-subtle);
  --breadcrumbs-variant-contained-border-radius: var(--global-radius-md);
  --breadcrumbs-variant-contained-padding: var(--global-spacing-3);

  --breadcrumbs-variant-outlined-background: transparent;
  --breadcrumbs-variant-outlined-border: 1px solid var(--color-border-default);
  --breadcrumbs-variant-outlined-border-radius: var(--global-radius-md);
  --breadcrumbs-variant-outlined-padding: var(--global-spacing-3);

  /* Breadcrumbs Collapsed */
  --breadcrumbs-collapsed-button-background: var(--color-surface-subtle);
  --breadcrumbs-collapsed-button-text: var(--color-text-secondary);
  --breadcrumbs-collapsed-button-border: 1px solid var(--color-border-subtle);
  --breadcrumbs-collapsed-button-border-radius: var(--global-radius-sm);
  --breadcrumbs-collapsed-button-padding: var(--global-spacing-2);
  --breadcrumbs-collapsed-button-icon-size: var(--global-icon-size-sm);

  --breadcrumbs-collapsed-button-hover-background: var(--color-surface-subtle-hover);
  --breadcrumbs-collapsed-button-hover-text: var(--color-text-primary);

  /* Breadcrumbs Dropdown */
  --breadcrumbs-dropdown-background: var(--color-background-primary);
  --breadcrumbs-dropdown-border: 1px solid var(--color-border-default);
  --breadcrumbs-dropdown-border-radius: var(--global-radius-md);
  --breadcrumbs-dropdown-shadow: var(--global-elevation-02);
  --breadcrumbs-dropdown-padding: var(--global-spacing-2);
  --breadcrumbs-dropdown-z-index: 1000;

  --breadcrumbs-dropdown-item-background: transparent;
  --breadcrumbs-dropdown-item-text: var(--color-text-primary);
  --breadcrumbs-dropdown-item-padding: var(--global-spacing-2);
  --breadcrumbs-dropdown-item-border-radius: var(--global-radius-sm);

  --breadcrumbs-dropdown-item-hover-background: var(--color-surface-subtle-hover);
  --breadcrumbs-dropdown-item-hover-text: var(--color-text-primary);

  --breadcrumbs-dropdown-item-active-background: var(--color-surface-interactive-default);
  --breadcrumbs-dropdown-item-active-text: var(--color-text-on-brand);
} 