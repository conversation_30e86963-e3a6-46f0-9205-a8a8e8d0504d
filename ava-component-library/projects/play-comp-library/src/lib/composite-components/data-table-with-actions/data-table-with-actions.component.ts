import {
  ChangeDetectionStrategy,
  Component,
  EventEmitter,
  Input,
  Output,
  ViewEncapsulation,
} from '@angular/core';
import { CommonModule } from '@angular/common';
import { CardComponent } from '../../components/card/card.component';
import { TableComponent } from '../../components/table/table.component';
import { ButtonComponent } from '../../components/button/button.component';
import { IconComponent } from '../../components/icon/icon.component';
import { CheckboxComponent } from '../../components/checkbox/checkbox.component';
import { PaginationControlsComponent } from '../../components/pagination-controls/pagination-controls.component';
import { DropdownComponent } from '../../components/dropdown/dropdown.component';
import { AvaTextboxComponent } from '../../components/textbox/ava-textbox.component';

export interface DataTableColumn {
  id: string;
  label: string;
  sortable?: boolean;
  width?: string;
  align?: 'left' | 'center' | 'right';
  render?: (value: unknown, row: Record<string, unknown>) => string;
}

export interface DataTableAction {
  id: string;
  label: string;
  icon?: string;
  variant?:
  | 'primary'
  | 'secondary'
  | 'success'
  | 'warning'
  | 'danger'
  | 'info'
  | 'purple'
  | 'emerald';
  size?: 'small' | 'medium' | 'large';
  disabled?: boolean;
  showForRow?: (row: Record<string, unknown>) => boolean;
  showForSelection?: boolean;
}

export interface BulkAction {
  id: string;
  label: string;
  icon?: string;
  variant?:
  | 'primary'
  | 'secondary'
  | 'success'
  | 'warning'
  | 'danger'
  | 'info'
  | 'purple'
  | 'emerald';
  disabled?: boolean;
}

export interface DataTableConfig {
  columns: DataTableColumn[];
  actions: DataTableAction[];
  bulkActions?: BulkAction[];
  showSearch?: boolean;
  showPagination?: boolean;
  showBulkActions?: boolean;
  showSelectAll?: boolean;
  itemsPerPage?: number;
  searchPlaceholder?: string;
  emptyMessage?: string;
  loadingMessage?: string;
}

export interface DataTableEvent {
  type:
  | 'row-action'
  | 'bulk-action'
  | 'selection-change'
  | 'page-change'
  | 'search'
  | 'sort';
  data: unknown;
  selectedRows?: Record<string, unknown>[];
  page?: number;
  searchTerm?: string;
  sortColumn?: string;
  sortDirection?: 'asc' | 'desc';
}

@Component({
  selector: 'ava-data-table-with-actions',
  standalone: true,
  imports: [
    CommonModule,
    CardComponent,
    ButtonComponent,
    IconComponent,
    CheckboxComponent,
    PaginationControlsComponent,
    // DropdownComponent,
    AvaTextboxComponent,
  ],
  templateUrl: './data-table-with-actions.component.html',
  styleUrl: './data-table-with-actions.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
  encapsulation: ViewEncapsulation.None,
})
export class DataTableWithActionsComponent {
  @Input() config: DataTableConfig = {
    columns: [],
    actions: [],
    showSearch: true,
    showPagination: true,
    showBulkActions: true,
    showSelectAll: true,
    itemsPerPage: 10,
    searchPlaceholder: 'Search...',
    emptyMessage: 'No data available',
    loadingMessage: 'Loading...',
  };

  @Input() data: Record<string, unknown>[] = [];
  @Input() loading = false;
  @Input() disabled = false;
  @Input() totalItems = 0;
  @Input() currentPage = 1;

  @Output() actionClick = new EventEmitter<{
    actionId: string;
    row: Record<string, unknown>;
  }>();
  @Output() bulkActionClick = new EventEmitter<{
    actionId: string;
    selectedRows: Record<string, unknown>[];
  }>();
  @Output() selectionChange = new EventEmitter<Record<string, unknown>[]>();
  @Output() pageChange = new EventEmitter<number>();
  @Output() searchChange = new EventEmitter<string>();
  @Output() sortChange = new EventEmitter<{
    column: string;
    direction: 'asc' | 'desc';
  }>();
  @Output() dataTableEvent = new EventEmitter<DataTableEvent>();

  selectedRows = new Set<Record<string, unknown>>();
  searchTerm = '';
  sortColumn = '';
  sortDirection: 'asc' | 'desc' = 'asc';

  get selectedRowsArray(): Record<string, unknown>[] {
    return Array.from(this.selectedRows);
  }

  trackByRow(index: number): number {
    return index;
  }

  get hasSelection(): boolean {
    return this.selectedRows.size > 0;
  }

  get isAllSelected(): boolean {
    return this.data.length > 0 && this.selectedRows.size === this.data.length;
  }

  get isIndeterminate(): boolean {
    return (
      this.selectedRows.size > 0 && this.selectedRows.size < this.data.length
    );
  }

  get filteredData(): Record<string, unknown>[] {
    let filtered = [...this.data];

    // Apply search filter
    if (this.searchTerm) {
      const searchLower = this.searchTerm.toLowerCase();
      filtered = filtered.filter((row) =>
        Object.values(row).some((value) =>
          String(value).toLowerCase().includes(searchLower)
        )
      );
    }

    // Apply sorting
    if (this.sortColumn) {
      filtered.sort((a, b) => {
        const aVal = String(a[this.sortColumn] || '');
        const bVal = String(b[this.sortColumn] || '');

        if (aVal < bVal) return this.sortDirection === 'asc' ? -1 : 1;
        if (aVal > bVal) return this.sortDirection === 'asc' ? 1 : -1;
        return 0;
      });
    }

    return filtered;
  }

  onRowAction(actionId: string, row: Record<string, unknown>) {
    this.actionClick.emit({ actionId, row });
    this.emitDataTableEvent('row-action', { actionId, row });
  }

  onBulkAction(actionId: string) {
    const selectedRows = this.selectedRowsArray;
    this.bulkActionClick.emit({ actionId, selectedRows });
    this.emitDataTableEvent('bulk-action', { actionId, selectedRows });
  }

  onRowSelectionChange(row: Record<string, unknown>, selected: boolean) {
    if (selected) {
      this.selectedRows.add(row);
    } else {
      this.selectedRows.delete(row);
    }
    this.selectionChange.emit(this.selectedRowsArray);
    this.emitDataTableEvent('selection-change', this.selectedRowsArray);
  }

  onSelectAllChange(selected: boolean) {
    if (selected) {
      this.data.forEach((row) => this.selectedRows.add(row));
    } else {
      this.selectedRows.clear();
    }
    this.selectionChange.emit(this.selectedRowsArray);
    this.emitDataTableEvent('selection-change', this.selectedRowsArray);
  }

  onPageChange(page: number) {
    this.currentPage = page;
    this.pageChange.emit(page);
    this.emitDataTableEvent('page-change', { page });
  }

  onSearchChange(event: Event) {
    const target = event.target as HTMLInputElement;
    const term = target?.value || '';
    this.searchTerm = term;
    this.searchChange.emit(term);
    this.emitDataTableEvent('search', { searchTerm: term });
  }

  getTotalPages(): number {
    return Math.ceil(this.totalItems / (this.config.itemsPerPage || 10));
  }

  onSortChange(column: string) {
    if (this.sortColumn === column) {
      this.sortDirection = this.sortDirection === 'asc' ? 'desc' : 'asc';
    } else {
      this.sortColumn = column;
      this.sortDirection = 'asc';
    }
    this.sortChange.emit({
      column: this.sortColumn,
      direction: this.sortDirection,
    });
    this.emitDataTableEvent('sort', {
      column: this.sortColumn,
      direction: this.sortDirection,
    });
  }

  isRowSelected(row: Record<string, unknown>): boolean {
    return this.selectedRows.has(row);
  }

  shouldShowAction(
    action: DataTableAction,
    row: Record<string, unknown>
  ): boolean {
    if (action.showForRow) {
      return action.showForRow(row);
    }
    return true;
  }

  getCellValue(row: Record<string, unknown>, column: DataTableColumn): string {
    const value = row[column.id];
    if (column.render) {
      return column.render(value, row);
    }
    return String(value || '');
  }

  private emitDataTableEvent(type: DataTableEvent['type'], data: unknown) {
    const event: DataTableEvent = {
      type,
      data,
      selectedRows: this.selectedRowsArray,
    };
    this.dataTableEvent.emit(event);
  }
}
