<ava-card>
  <div header class="data-table-header">
    <div class="header-content">
      <h3 class="table-title">Data Table</h3>
      <div class="header-actions">
        <span *ngIf="hasSelection" class="selection-badge">
          {{ selectedRows.size }} selected
        </span>
      </div>
    </div>
  </div>

  <div content class="data-table-content">
    <!-- Search Section -->
    <div *ngIf="config.showSearch" class="search-section">
      <ava-textbox
        [placeholder]="config.searchPlaceholder || 'Search...'"
        [disabled]="disabled"
        icon="search"
        iconPosition="start"
        (textboxChange)="onSearchChange($event)"
      >
      </ava-textbox>
    </div>

    <!-- Bulk Actions Section -->
    <div
      *ngIf="config.showBulkActions && hasSelection"
      class="bulk-actions-section"
    >
      <div class="bulk-actions-grid">
        <ava-button
          *ngFor="let action of config.bulkActions"
          [variant]="action.variant || 'primary'"
          size="small"
          [disabled]="action.disabled || disabled"
          [label]="action.label"
          [iconName]="action.icon || ''"
          [iconSize]="16"
          (click)="onBulkAction(action.id)"
        >
        </ava-button>
      </div>
    </div>

    <!-- Table Section -->
    <div class="table-section">
      <div *ngIf="loading" class="loading-overlay">
        <div class="loading-content">
          <ava-icon
            iconName="loader"
            [iconSize]="24"
            iconColor="#007bff"
          ></ava-icon>
          <span>{{ config.loadingMessage || "Loading..." }}</span>
        </div>
      </div>

      <div *ngIf="!loading && filteredData.length === 0" class="empty-state">
        <ava-icon
          iconName="inbox"
          [iconSize]="48"
          iconColor="#6c757d"
        ></ava-icon>
        <p>{{ config.emptyMessage || "No data available" }}</p>
      </div>

      <div *ngIf="!loading && filteredData.length > 0" class="table-container">
        <table class="data-table">
          <thead>
            <tr>
              <!-- Select All Column -->
              <th *ngIf="config.showSelectAll" class="select-column">
                <ava-checkbox
                  [isChecked]="isAllSelected"
                  [indeterminate]="isIndeterminate"
                  [disable]="disabled"
                  (isCheckedChange)="onSelectAllChange($event)"
                >
                </ava-checkbox>
              </th>

              <!-- Data Columns -->
              <th
                *ngFor="let column of config.columns"
                class="data-column"
                [class.sortable]="column.sortable"
                [style.width]="column.width"
                [style.text-align]="column.align || 'left'"
                (click)="column.sortable ? onSortChange(column.id) : null"
              >
                <div class="column-header">
                  <span class="column-label">{{ column.label }}</span>
                  <ava-icon
                    *ngIf="column.sortable"
                    [iconName]="
                      sortColumn === column.id
                        ? sortDirection === 'asc'
                          ? 'chevron-up'
                          : 'chevron-down'
                        : 'chevron-up'
                    "
                    [iconSize]="12"
                    iconColor="#666"
                  >
                  </ava-icon>
                </div>
              </th>

              <!-- Actions Column -->
              <th *ngIf="config.actions.length > 0" class="actions-column">
                Actions
              </th>
            </tr>
          </thead>
          <tbody>
            <tr
              *ngFor="let row of filteredData; trackBy: trackByRow"
              class="table-row"
              [class.selected]="isRowSelected(row)"
            >
              <!-- Select Row Column -->
              <td *ngIf="config.showSelectAll" class="select-column">
                <ava-checkbox
                  [isChecked]="isRowSelected(row)"
                  [disable]="disabled"
                  (isCheckedChange)="onRowSelectionChange(row, $event)"
                >
                </ava-checkbox>
              </td>

              <!-- Data Cells -->
              <td
                *ngFor="let column of config.columns"
                class="data-cell"
                [style.text-align]="column.align || 'left'"
              >
                {{ getCellValue(row, column) }}
              </td>

              <!-- Actions Cell -->
              <td *ngIf="config.actions.length > 0" class="actions-cell">
                <div class="row-actions">
                  <ava-button
                    *ngFor="let action of config.actions"
                    [variant]="action.variant || 'secondary'"
                    size="small"
                    [disabled]="
                      action.disabled ||
                      disabled ||
                      !shouldShowAction(action, row)
                    "
                    [label]="action.label"
                    [iconName]="action.icon || ''"
                    [iconSize]="14"
                    (click)="onRowAction(action.id, row)"
                  >
                  </ava-button>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>

    <!-- Pagination Section -->
    <div
      *ngIf="config.showPagination && totalItems > 0"
      class="pagination-section"
    >
      <ava-pagination-controls
        [currentPage]="currentPage"
        [totalPages]="getTotalPages()"
        (pageChange)="onPageChange($event)"
      >
      </ava-pagination-controls>
    </div>
  </div>
</ava-card>
