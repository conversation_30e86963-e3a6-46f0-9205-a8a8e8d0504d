<div class="chat-window">
  <div class="chat-main">
    <!-- Messages area that scrolls -->
    <div class="messages-container" #messagesContainer>
      @for (message of messages; track message.id) {
        <div class="message-wrapper" [class.user-message]="message.isUser" [class.bot-message]="!message.isUser">
          @if (!message.isUser) {
            <div class="message-content">
              <ava-default-card class="message-card bot-card">
                <div content>
                  <p class="message-text">{{ message.text }}</p>
                  <div class="message-timestamp">{{ message.timestamp }}</div>
                </div>
              </ava-default-card>
            </div>
          } @else {
            <div class="message-content">
              <ava-default-card class="message-card user-card">
                <div content>
                  <p class="message-text">{{ message.text }}</p>
                  <div class="message-timestamp">{{ message.timestamp }}</div>
                </div>
              </ava-default-card>
            </div>
          }
        </div>
      }
    </div>

    <!-- Fixed input area at bottom -->
    <div class="input-area">
      <ava-textbox
        [placeholder]="placeholder"
        [value]="currentMessage()"
        [disabled]="disabled"
        (keydown)="onKeyPress($event)"
        (input)="onInput($event)"
        (click)="focusInput($event)"
        class="chat-input">
        <ava-button
          slot="icon-end"
          label=""
          variant="primary"
          size="medium"
          [pill]="true"
          iconName="send"
          iconPosition="only"
          (userClick)="sendMessage()"
          [disabled]="!currentMessage().trim() || disabled"
          class="send-button">
        </ava-button>
      </ava-textbox>
    </div>
  </div>
</div>
