import { ChangeDetectionStrategy, Component, Input, Output, EventEmitter, signal, ViewChild, ElementRef, AfterViewChecked, OnInit, AfterViewInit, OnChanges, SimpleChanges } from '@angular/core';
import { CommonModule } from '@angular/common';
import { AvaTextboxComponent } from '../../components/textbox/ava-textbox.component';
import { DefaultCardComponent } from '../../components/card/default-card/default-card.component';
import { ButtonComponent } from '../../components/button/button.component';

export interface ChatMessage {
  id: string;
  text: string;
  timestamp: string;
  isUser: boolean;
  avatar?: string;
}

@Component({
  selector: 'ava-chat-window',
  standalone: true,
  imports: [CommonModule, AvaTextboxComponent, DefaultCardComponent, ButtonComponent],
  templateUrl: './chat-window.component.html',
  styleUrl: './chat-window.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class ChatWindowComponent implements AfterViewChecked, AfterViewInit, OnChanges {
  @Input() messages: ChatMessage[] = [];
  @Input() placeholder = 'Type a message';
  @Input() disabled = false;

  @Output() messageSent = new EventEmitter<string>();

  @ViewChild('messagesContainer') private messagesContainer!: ElementRef;

  currentMessage = signal('');
  private shouldScrollToBottom = false;

  ngOnChanges(changes: SimpleChanges) {
    // Scroll to bottom when messages array changes
    if (changes['messages'] && changes['messages'].currentValue) {
      this.shouldScrollToBottom = true;
    }
  }

  ngAfterViewInit() {
    // Scroll to bottom when component loads
    setTimeout(() => {
      this.scrollToBottom();
    }, 100);
  }

  ngAfterViewChecked() {
    if (this.shouldScrollToBottom) {
      this.scrollToBottom();
      this.shouldScrollToBottom = false;
    }
  }

  sendMessage() {
    const messageText = this.currentMessage().trim();
    if (!messageText || this.disabled) return;

    this.messageSent.emit(messageText);
    this.currentMessage.set('');
    this.shouldScrollToBottom = true;
  }

  onKeyPress(event: KeyboardEvent) {
    if (event.key === 'Enter' && !event.shiftKey) {
      event.preventDefault();
      this.sendMessage();
    }
  }

  onInput(event: Event) {
    const target = event.target as HTMLInputElement;
    this.currentMessage.set(target.value);
  }

  focusInput(event: Event) {
    const target = event.target as HTMLInputElement;
    target.focus();
  }

  private scrollToBottom() {
    if (this.messagesContainer) {
      const element = this.messagesContainer.nativeElement;
      // Use requestAnimationFrame for better timing
      requestAnimationFrame(() => {
        element.scrollTop = element.scrollHeight;
      });
    }
  }
}
