import { ChangeDetectionStrategy, Component, EventEmitter, Input, Output, ViewChild } from '@angular/core';
import { PopupComponent } from '../../components/popup/popup.component';
import { CommonModule } from '@angular/common';
import { AvaTextareaComponent } from '../../components/textarea/ava-textarea.component';

@Component({
  selector: 'ava-confirmation-popup',
  standalone: true,
  imports: [CommonModule, PopupComponent, AvaTextareaComponent],
  templateUrl: './confirmation-popup.component.html',
  styleUrl: './confirmation-popup.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class ConfirmationPopupComponent {
  @Input() confirmationLabel = 'Yes';
  @Input() title = 'title';
  @Input() message = 'message';
  @Input() show = false;
  @ViewChild('feedbackText') feedbackTextRef: any;
  @Output() confirm = new EventEmitter<string>();
  @Output() cancel = new EventEmitter<void>();
  @Output() closed = new EventEmitter<void>();
  handleConfirm(): void {
    this.confirm.emit(this.feedbackTextRef.value);
  }
  handleCancel(): void {
    this.show = false;
    this.cancel.emit();
  }
  handleClose(): void {
    this.closed.emit();
    this.show = false;
  }
}
