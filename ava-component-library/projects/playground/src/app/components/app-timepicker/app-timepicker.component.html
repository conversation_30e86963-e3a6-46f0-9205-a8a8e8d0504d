<div class="documentation container">
  <div class="row">
    <div class="col-12">
      <header class="doc-header">
        <h1>Time Picker Component</h1>
        <p class="description">
          A versatile time picker component that allows users to select time. Built with accessibility and user experience in mind. Users can input time in the <strong>hh:mm AM/PM</strong> format, where <strong>hh</strong> ranges from 1 to 12 and <strong>mm</strong> ranges from 00 to 59. The selected time will be automatically marked in the picker.
        </p>
      </header>
    </div>
  </div>

  <div class="row">
    <div class="col-12">
      <section class="doc-section">
        <h2>Installation</h2>
        <div class="code-block">
          <pre><code>import {{ '{' }} TimePickerComponent {{ '}' }} from '&#64;awe/play-comp-library';</code></pre>
        </div>
      </section>
    </div>
  </div>

  <div class="doc-sections">
    <section class="doc-section" *ngFor="let section of sections; let i = index">
      <div class="row">
        <div class="col-12">
          <div class="section-header" tabindex="0" role="button">
            <h2>{{ section.title }}</h2>
            <div class="description-container">
              <p>{{ section.description }}</p>
              <div class="code-toggle" (click)="toggleCodeVisibility(i, $event)">
                <span *ngIf="!section.showCode">View Code</span>
                <span *ngIf="section.showCode">Hide Code</span>
                <awe-icons [iconName]="section.showCode ? 'awe_arrow_back_up' : 'awe_arrow_back_down'" iconColor="action"></awe-icons>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="code-example" [class.expanded]="section.showCode">
        <div class="example-preview">
          <ng-container [ngSwitch]="section.title">
            <ng-container *ngSwitchCase="'Basic Usage'">
              <div class="row g-3">
                <div class="col-12 col-sm-auto">
                  <awe-time-picker (timeSelected)="onTimeSelected($event)"></awe-time-picker>
                </div>
              </div>
            </ng-container>

            <ng-container *ngSwitchCase="'Custom Icon'">
              <div class="row g-3">
                <div class="col-12 col-sm-auto">
                  <awe-time-picker [iconName]="'awe_tick'" (timeSelected)="onTimeSelected($event)"></awe-time-picker>
                </div>
              </div>
            </ng-container>

            <ng-container *ngSwitchCase="'Disabled Input'">
              <div class="row g-3">
                <div class="col-12 col-sm-auto">
                  <awe-time-picker [inputDisabled]="true" (timeSelected)="onTimeSelected($event)"></awe-time-picker>
                </div>
              </div>
            </ng-container>

            <ng-container *ngSwitchCase="'Disabled Icon'">
              <div class="row g-3">
                <div class="col-12 col-sm-auto">
                  <awe-time-picker [iconDisabled]="true" (timeSelected)="onTimeSelected($event)"></awe-time-picker>
                </div>
              </div>
            </ng-container>

            <ng-container *ngSwitchCase="'Present Time'">
              <div class="row g-3">
                <div class="col-12 col-sm-auto">
                  <awe-time-picker [presentDate]="true" (timeSelected)="onTimeSelected($event)"></awe-time-picker>
                </div>
              </div>
            </ng-container>

            <ng-container *ngSwitchCase="'Apply and Click'">
              <div class="row g-3">
                <div class="col-12 col-sm-auto">
                  <awe-time-picker [apply]="true" [click]="false" (timeSelected)="onTimeSelected($event)"></awe-time-picker>
                </div>
              </div>
            </ng-container>

            <ng-container *ngSwitchCase="'Click to Close'">
              <div class="row g-3">
                <div class="col-12 col-sm-auto">
                  <awe-time-picker [click]="true" (timeSelected)="onTimeSelected($event)"></awe-time-picker>
                </div>
              </div>
            </ng-container>

            <ng-container *ngSwitchCase="'Error Message'">
              <div class="row g-3">
                <div class="col-12 col-sm-auto">
                  <awe-time-picker
                    [click]="true"
                    (timeSelected)="onTimeSelected($event)"
                    [errorMessage]="'Invalid format.'"
                  ></awe-time-picker>
                </div>
              </div>
            </ng-container>
          </ng-container>
        </div>
        <div class="code-block" *ngIf="section.showCode">
          <div class="code-content">
            <pre><code [innerText]="getTimePickerCode(section.title)"></code></pre>
          </div>
          <button class="copy-button" (click)="copyCode(section.title.toLowerCase())">
            <awe-icons iconName="awe_copy"></awe-icons>
          </button>
        </div>
      </div>
    </section>
  </div>

  <section class="doc-section api-reference">
    <h2>API Reference</h2>
    <table class="api-table">
      <thead>
        <tr>
          <th>Property</th>
          <th>Type</th>
          <th>Default</th>
          <th>Description</th>
        </tr>
      </thead>
      <tbody>
        <tr *ngFor="let prop of apiProps">
          <td><code>{{ prop.name }}</code></td>
          <td><code>{{ prop.type }}</code></td>
          <td><code>{{ prop.default }}</code></td>
          <td>{{ prop.description }}</td>
        </tr>
      </tbody>
    </table>
  </section>

  <!-- Events -->
<div class="row">
  <div class="col-12">
    <section class="doc-section">
      <h2>Events</h2>
      <table class="api-table">
        <thead>
          <tr>
            <th>Event</th>
            <th>Type</th>
            <th>Description</th>
          </tr>
        </thead>
        <tbody>
          <tr>
            <td><code>timeSelected</code></td>
            <td><code>EventEmitter&lt;string&gt;</code></td>
            <td>Event emitted when a time is selected. The emitted value is the selected time in hh:mm AM/PM format.</td>
          </tr>
        </tbody>
      </table>
    </section>
  </div>
</div>

</div>
