import { Component, signal } from '@angular/core';
import { ChatWindowComponent, ChatMessage } from '../../../../../play-comp-library/src/lib/composite-components/chat-window/chat-window.component';

@Component({
  selector: 'app-chat-window',
  standalone: true,
  imports: [ChatWindowComponent],
  templateUrl: './app-chat-window.component.html',
  styleUrls: ['./app-chat-window.component.scss']
})
export class AppChatWindowComponent {
  messages = signal<ChatMessage[]>([]);
  placeholder = 'Type your message here...';
  disabled = false;

  onMessageSent(messageText: string) {
    // Add user message
    const userMessage: ChatMessage = {
      id: Date.now().toString(),
      text: messageText,
      timestamp: new Date().toLocaleString('en-US', {
        hour: 'numeric',
        minute: '2-digit',
        hour12: true
      }) + ' | Today',
      isUser: true
    };

    this.messages.update(messages => [...messages, userMessage]);

    // Add bot response after delay
    setTimeout(() => {
      const botMessage: ChatMessage = {
        id: (Date.now() + 1).toString(),
        text: this.generateBotResponse(messageText),
        timestamp: new Date().toLocaleString('en-US', {
          hour: 'numeric',
          minute: '2-digit',
          hour12: true
        }) + ' | Today',
        isUser: false,
        avatar: '🤖'
      };

      this.messages.update(messages => [...messages, botMessage]);
    }, 1000);
  }

  private generateBotResponse(userMessage: string): string {
    const lowerMessage = userMessage.toLowerCase().trim();

    if (lowerMessage === 'hi' || lowerMessage === 'hello' || lowerMessage === 'hey') {
      return 'Hi! How can I help you today?';
    }

    if (lowerMessage.includes('help')) {
      return 'I\'m here to help! What do you need assistance with?';
    }

    if (lowerMessage.includes('thank')) {
      return 'You\'re welcome! Is there anything else I can help you with?';
    }

    return 'Thanks for your message! How can I assist you?';
  }
}
