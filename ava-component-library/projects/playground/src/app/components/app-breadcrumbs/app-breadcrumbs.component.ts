import { Component, ViewEncapsulation } from '@angular/core';
import { BreadcrumbsComponent } from "../../../../../play-comp-library/src/lib/components/breadcrumbs/breadcrumbs.component";
import { IconComponent } from "../../../../../play-comp-library/src/lib/components/icon/icon.component";
import { CommonModule } from '@angular/common';

interface ApiProperty {
  name: string;
  type: string;
  default: string;
  description: string;
}

@Component({
  selector: 'app-app-breadcrumbs',
  imports: [BreadcrumbsComponent, IconComponent, CommonModule],
  templateUrl: './app-breadcrumbs.component.html',
  styleUrl: './app-breadcrumbs.component.scss',
  encapsulation: ViewEncapsulation.None
})
export class AppBreadcrumbsComponent {
   
  // Breadcrumbs Array
  breadcrumbs = [
    { label: 'Home', url: '/home', active: false },
    { label: 'Category', url: '/category', active: false },
    { label: 'Product', url: '/product', active: true }
  ];

  // Documentation Sections
  sections = [
    {
      title: 'Basic Usage',
      description: 'Basic usage of the breadcrumbs component.',
      showCode: false
    },
    {
      title: 'Breadcrumbs Sizes',
      description: 'Adjust the size of the breadcrumbs: small, medium, or large.',
      showCode: false
    }
  ];

  // API Documentation
  apiProps: ApiProperty[] = [
    { name: 'breadcrumbs', type: 'Array<{ label: string, url: string, active: boolean }>', default: '[]', description: 'Array of breadcrumb items to display.' },
    { name: 'sizeClass', type: "'small' | 'medium' | 'large'", default: "'medium'", description: 'Sets the size of the breadcrumbs.' }
  ];

  // Events
  events = [
    { name: 'onBreadcrumbClick', type: 'EventEmitter<{ label: string, url: string }>', description: 'Emitted when a breadcrumb item is clicked.' }
  ];

  // Toggle Section Expansion
  toggleSection(index: number): void {
    this.sections[index].showCode = !this.sections[index].showCode;
  }

  toggleCodeVisibility(index: number, event: MouseEvent): void {
    event.stopPropagation(); // Prevent the click event from bubbling up to the section header
    this.sections[index].showCode = !this.sections[index].showCode;
  }

  // Get Example Code for a Section
  getExampleCode(section: string): string {
    const examples: Record<string, string> = {
      'basic usage': `<ava-breadcrumbs [breadcrumbs]="breadcrumbs"></ava-breadcrumbs>`,
      'breadcrumbs sizes': `
        <ava-breadcrumbs [breadcrumbs]="breadcrumbs" sizeClass="small"></ava-breadcrumbs>
        <ava-breadcrumbs [breadcrumbs]="breadcrumbs" sizeClass="medium"></ava-breadcrumbs>
        <ava-breadcrumbs [breadcrumbs]="breadcrumbs" sizeClass="large"></ava-breadcrumbs>
      `
    };
    return examples[section] || '';
  }

  // Copy Code to Clipboard
  copyCode(section: string): void {
    const code = this.getExampleCode(section);
    navigator.clipboard.writeText(code).then(() => {
      console.log('Code copied to clipboard');
    }).catch(err => {
      console.error('Failed to copy code:', err);
    });
  }
}
