.list-skeleton-demo {
  max-width: 900px;
  margin: 0 auto;

  .demo-header {
    text-align: center;
    margin-bottom: 2rem;

    h2 {
      color: #333;
      margin-bottom: 0.5rem;
    }

    p {
      color: #666;
      font-size: 1.1rem;
    }
  }

  .demo-content {
    margin-bottom: 3rem;

    .list-skeleton-container {
      padding: 2rem;

      .table-skeleton {
        background: white;
        border-radius: 6px;
        overflow: hidden;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

        .table-header {
          display: flex;
          background: #f8f9fa;
          border-bottom: 1px solid #e9ecef;
          padding: 1rem;

          .header-cell {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 0.5rem;
            flex: 1;
            padding: 0 0.5rem;

            .element-label {
              font-size: 0.8rem;
              color: #666;
              font-style: italic;
            }
          }
        }

        .table-row {
          display: flex;
          border-bottom: 1px solid #e9ecef;
          padding: 1rem;

          &:last-child {
            border-bottom: none;
          }

          .table-cell {
            display: flex;
            justify-content: center;
            flex: 1;
            padding: 0 0.5rem;
          }
        }
      }
    }
  }

  .demo-info {
    h3 {
      color: #333;
      margin-bottom: 1rem;
      border-bottom: 2px solid #007bff;
      padding-bottom: 0.5rem;
    }

    p {
      color: #555;
      line-height: 1.6;
      margin-bottom: 1.5rem;
    }

    pre {
      background: #f8f9fa;
      border: 1px solid #e9ecef;
      border-radius: 4px;
      padding: 1rem;
      overflow-x: auto;
      margin: 1rem 0;

      code {
        color: #333;
        font-family: "Courier New", monospace;
        font-size: 0.9rem;
      }
    }
  }
}
