.card-skeleton-demo {
  max-width: 500px;
  margin: 0 auto;

  .demo-header {
    text-align: center;
    margin-bottom: 2rem;

    h2 {
      color: #333;
      margin-bottom: 0.5rem;
    }

    p {
      color: #666;
      font-size: 1.1rem;
    }
  }

  .demo-content {
    margin-bottom: 3rem;

    .card-skeleton-container {
      display: flex;
      justify-content: center;
      padding: 2rem;

      .card-skeleton {
        width: 700px;
        background: white;
        border-radius: 8px;
        padding: 1.5rem;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        display: flex;
        flex-direction: column;
        gap: 1rem;

        .skeleton-element {
          display: flex;
          flex-direction: column;
          gap: 0.5rem;

          .element-label {
            font-size: 0.8rem;
            color: #666;
            font-style: italic;
          }

          &.title {
            margin-top: 0.5rem;
          }

          &.description {
            margin-top: 0.25rem;
          }
        }
      }
    }
  }

  .demo-info {
    h3 {
      color: #333;
      margin-bottom: 1rem;
      border-bottom: 2px solid #007bff;
      padding-bottom: 0.5rem;
    }

    p {
      color: #555;
      line-height: 1.6;
      margin-bottom: 1.5rem;
    }

    pre {
      background: #f8f9fa;
      border: 1px solid #e9ecef;
      border-radius: 4px;
      padding: 1rem;
      overflow-x: auto;
      margin: 1rem 0;

      code {
        color: #333;
        font-family: "Courier New", monospace;
        font-size: 0.9rem;
      }
    }
  }
}
