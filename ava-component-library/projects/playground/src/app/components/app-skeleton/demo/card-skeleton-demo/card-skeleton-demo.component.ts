import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { SkeletonComponent } from '../../../../../../../play-comp-library/src/public-api';

@Component({
  selector: 'ava-card-skeleton-demo',
  standalone: true,
  imports: [CommonModule, SkeletonComponent],
  templateUrl: './card-skeleton-demo.component.html',
  styleUrl: './card-skeleton-demo.component.scss',
})
export class CardSkeletonDemoComponent {
  // Demo data for card skeleton
  cardSkeletonElements = [
    {
      width: '100%',
      height: '200px',
      shape: 'rounded' as const,
      animation: 'wave' as const,
      description: 'Card Image',
    },
    {
      width: '70%',
      height: '20px',
      shape: 'rectangle' as const,
      animation: 'wave' as const,
      description: 'Card Title',
    },
    {
      width: '100%',
      height: '16px',
      shape: 'rectangle' as const,
      animation: 'wave' as const,
      description: 'Card Description Line 1',
    },
    {
      width: '100%',
      height: '16px',
      shape: 'rectangle' as const,
      animation: 'wave' as const,
      description: 'Card Description Line 2',
    },
    {
      width: '40%',
      height: '16px',
      shape: 'rectangle' as const,
      animation: 'wave' as const,
      description: 'Card Description Line 3',
    },
  ];
}
