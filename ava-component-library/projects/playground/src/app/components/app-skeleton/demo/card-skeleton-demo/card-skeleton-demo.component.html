<div class="card-skeleton-demo">
  <div class="demo-content">
    <div class="card-skeleton-container">
      <div class="card-skeleton">
        <div
          *ngFor="let element of cardSkeletonElements; let i = index"
          class="skeleton-element"
          [class.title]="i === 1"
          [class.description]="i > 1"
        >
          <ava-skeleton
            [width]="element.width"
            [height]="element.height"
            [shape]="element.shape"
            [animation]="element.animation"
          ></ava-skeleton>
          <!-- <span class="element-label">{{ element.description }}</span> -->
        </div>
      </div>
    </div>
  </div>
</div>
