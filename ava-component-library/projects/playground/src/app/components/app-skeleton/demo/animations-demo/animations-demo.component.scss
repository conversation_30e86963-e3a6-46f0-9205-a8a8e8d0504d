.animations-demo {
  padding: 2rem;
  max-width: 900px;
  margin: 0 auto;

  .demo-header {
    text-align: center;
    margin-bottom: 2rem;

    h2 {
      color: #333;
      margin-bottom: 0.5rem;
    }

    p {
      color: #666;
      font-size: 1.1rem;
    }
  }

  .demo-content {
    margin-bottom: 3rem;

    .animations-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
      gap: 2rem;
      padding: 2rem;
      background: #f8f9fa;
      border-radius: 8px;
      border: 1px solid #e9ecef;

      .animation-item {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 1rem;
        padding: 1.5rem;
        background: white;
        border-radius: 6px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

        .animation-preview {
          display: flex;
          justify-content: center;
          align-items: center;
          min-height: 120px;
        }

        .animation-info {
          text-align: center;

          h4 {
            color: #333;
            margin-bottom: 0.5rem;
            font-size: 1.1rem;
          }

          p {
            color: #666;
            font-size: 0.9rem;
            line-height: 1.4;
            margin: 0;
          }
        }
      }
    }
  }

  .demo-info {
    h3 {
      color: #333;
      margin-bottom: 1rem;
      border-bottom: 2px solid #007bff;
      padding-bottom: 0.5rem;
    }

    ul {
      color: #555;
      line-height: 1.6;
      margin-bottom: 1.5rem;
      padding-left: 1.5rem;

      li {
        margin-bottom: 0.5rem;
      }
    }

    pre {
      background: #f8f9fa;
      border: 1px solid #e9ecef;
      border-radius: 4px;
      padding: 1rem;
      overflow-x: auto;
      margin: 1rem 0;

      code {
        color: #333;
        font-family: "Courier New", monospace;
        font-size: 0.9rem;
      }
    }
  }
}
