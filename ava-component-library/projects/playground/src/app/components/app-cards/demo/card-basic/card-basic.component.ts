import { Component } from '@angular/core';
import { RouterModule } from '@angular/router';
import { CardComponent } from '../../../../../../../play-comp-library/src/lib/components/card/card.component';
import { TxtCardComponent } from '../../../../../../../play-comp-library/src/lib/composite-components/txt-card/txt-card.component';
import { AvaTagComponent } from '../../../../../../../play-comp-library/src/lib/components/tags/tags.component';
import { IconComponent } from '../../../../../../../play-comp-library/src/lib/components/icon/icon.component';
import { ButtonComponent } from '../../../../../../../play-comp-library/src/lib/components/button/button.component';
import { ApprovalCardComponent } from '../../../../../../../play-comp-library/src/lib/composite-components/approval-card/approval-card.component';
import { DefaultCardComponent } from '../../../../../../../play-comp-library/src/lib/components/card/default-card/default-card.component';

@Component({
  selector: 'app-card-basic',
  standalone: true,
  imports: [
    RouterModule,
    TxtCardComponent,
    CardComponent,
    AvaTagComponent,
    IconComponent,
    ButtonComponent,
    ApprovalCardComponent,
    DefaultCardComponent,
  ],
  templateUrl: './card-basic.component.html',
  styleUrl: './card-basic.component.scss',
})
export class AppCardBasicComponent {
  uClick(e: any) {
    console.log('Card clicked:', e);
  }
}
