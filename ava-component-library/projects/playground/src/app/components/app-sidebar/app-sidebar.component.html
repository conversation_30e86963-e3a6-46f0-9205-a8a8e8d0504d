<div class="sidebar">
  <h2>Ava-Play</h2>
  <div class="theme-wrapper">
    <button [ngClass]="{ active: !active }" (click)="dark()">Darktheme</button>
    <button [ngClass]="{ active: active }" (click)="light()">
      Light Theme
    </button>
  </div>
  <ul>
    <li><a routerLink="/app-button">Button</a></li>
    <li><a routerLink="/app-toggle">Toggle</a></li>
    <li><a routerLink="/app-checkbox">Checkbox</a></li>
    <li><a routerLink="/app-spinners">Spinners</a></li>
    <li><a routerLink="/app-icons">Icons</a></li>
    <li><a routerLink="/app-tabs">Tabs</a></li>
    <li><a routerLink="/app-textbox">Textbox</a></li>
    <li><a routerLink="/app-accordion">Accordion</a></li>
    <li><a routerLink="/app-badges">Badges</a></li>
    <li><a routerLink="/app-pagination">Pagination Controls</a></li>
    <li><a routerLink="/app-avatars">Avatars</a></li>
    <li><a routerLink="/app-cards">Cards</a></li>
    <li><a routerLink="/app-stepper">Stepper</a></li>
    <li><a routerLink="/app-textarea">Textarea</a></li>
    <li><a routerLink="/app-autocomplete">Autocomplete</a></li>
    <li><a routerLink="/app-dropdown">Dropdown</a></li>
    <li><a routerLink="/app-radiobutton">Radio Button</a></li>
    <li><a routerLink="/app-tags">Tags</a></li>
    <li><a routerLink="/app-sidebar">Sidebar</a></li>
    <li><a routerLink="/app-popup">Popup</a></li>
    <li><a routerLink="/app-date-input">Calendar</a></li>
    <li><a routerLink="/app-links">Links</a></li>
    <li><a routerLink="/app-table">Table</a></li>
    <li><a routerLink="/app-file-upload">File Upload</a></li>
    <li><a routerLink="/app-app-snackbar">Snackbar</a></li>
    <li><a routerLink="/app-slider">Slider</a></li>
    <li><a routerLink="/app-file-attach-pill">File Attach Pill</a></li>
    <li><a routerLink="/app-progress-bar">Progress bar</a></li>
    <li><a routerLink="/app-list">List</a></li>
    <li><a routerLink="/app-tooltip">Tooltip</a></li>
    <li><a routerLink="/app-dividers">Dividers</a></li>
    <li><a routerLink="/high-contrast-test">High Contrast Test</a></li>
  </ul>
</div>
