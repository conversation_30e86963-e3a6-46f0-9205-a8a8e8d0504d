<div class="documentation">
  <div class="row">
    <div class="col-12">
      <header class="doc-header">
        <h1>Dropdown Component</h1>
        <p class="description">
          A versatile dropdown component that supports various configurations
          such as sub-options, icons, animations, and multi-select
          functionality. Built with accessibility and user experience in mind.
        </p>
      </header>
    </div>
  </div>

  <div class="row">
    <div class="col-12">
      <section class="doc-section">
        <h2>Installation</h2>
        <div class="code-block">
          <pre><code>import {{ '{' }} DropdownComponent {{ '}' }} from '&#64;awe/play-comp-library';</code></pre>
        </div>
      </section>
    </div>
  </div>
  <div class="demo-navigation">
    <h3>Demo Sections</h3>
    <div class="nav-links">
      <a routerLink="/dropdown/basic-usage" class="nav-link">Basic Usage</a>
      <a routerLink="/dropdown/default-selection" class="nav-link"
        >Default Selection</a
      >
      <a routerLink="/dropdown/search" class="nav-link">Search</a>
      <a routerLink="/dropdown/icons" class="nav-link">Icons</a>
      <a routerLink="/dropdown/multi-select" class="nav-link">Multi-Select</a>
      <a routerLink="/dropdown/single-select" class="nav-link">Single-Select</a>
      <a routerLink="/dropdown/disabled" class="nav-link">Disabled States</a>
      <a routerLink="/dropdown/nested" class="nav-link">Nested Structure</a>
      <a routerLink="/dropdown/api" class="nav-link">API Reference</a>
    </div>
  </div>

  <div class="demo-content">
    <router-outlet></router-outlet>
  </div>

  <div class="doc-sections">
    <section
      class="doc-section"
      *ngFor="let section of sections; let i = index"
    >
      <div class="row">
        <div class="col-12">
          <div class="section-header" tabindex="0" role="button">
            <h2>{{ section.title }}</h2>
            <div class="description-container">
              <p>{{ section.description }}</p>
              <button
                class="code-toggle"
                (click)="toggleCodeVisibility(i, $event)"
                (keyup.enter)="toggleCodeVisibility(i, $event)"
                (keyup.space)="toggleCodeVisibility(i, $event)"
                type="button"
              >
                <span *ngIf="!section.showCode">View Code</span>
                <span *ngIf="section.showCode">Hide Code</span>
                <!-- <awe-icons [iconName]="section.showCode ? 'awe_arrow_back_up' : 'awe_arrow_back_down'" iconColor="action"></awe-icons> -->
              </button>
            </div>
          </div>
        </div>
      </div>

      <div class="code-example" [class.expanded]="section.showCode">
        <div class="example-preview">
          <ng-container [ngSwitch]="section.title">
            <ng-container *ngSwitchCase="'Basic Usage'">
              <div
                style="
                  display: flex;
                  flex-wrap: wrap;
                  gap: 1rem;
                  align-items: flex-start;
                "
              >
                <ava-dropdown
                  dropdownTitle="Select Category"
                  [options]="basicOptions"
                  [suboptions]="basicSuboptions"
                  (selectionChange)="onSelectionChange($event)"
                >
                </ava-dropdown>
                <ava-dropdown
                  dropdownTitle="Simple Options"
                  [options]="simpleOptions"
                  (selectionChange)="onSelectionChange($event)"
                >
                </ava-dropdown>
              </div>
            </ng-container>

            <ng-container *ngSwitchCase="'With Default Selection'">
              <div
                style="
                  display: flex;
                  flex-wrap: wrap;
                  gap: 1rem;
                  align-items: flex-start;
                "
              >
                <ava-dropdown
                  dropdownTitle="Select Fruit"
                  [options]="defaultSelectionOptions"
                  [suboptions]="defaultSelectionSuboptions"
                  [selectedValue]="'Banana'"
                  (selectionChange)="onSelectionChange($event)"
                >
                </ava-dropdown>
                <ava-dropdown
                  dropdownTitle="Simple Default"
                  [options]="simpleOptions"
                  [selectedValue]="'Option 3'"
                  (selectionChange)="onSelectionChange($event)"
                >
                </ava-dropdown>
              </div>
            </ng-container>

            <ng-container *ngSwitchCase="'With Search'">
              <div
                style="
                  display: flex;
                  flex-wrap: wrap;
                  gap: 1rem;
                  align-items: flex-start;
                "
              >
                <ava-dropdown
                  dropdownTitle="Search Categories"
                  [options]="basicOptions"
                  [suboptions]="basicSuboptions"
                  [search]="true"
                  (selectionChange)="onSelectionChange($event)"
                >
                </ava-dropdown>
                <ava-dropdown
                  dropdownTitle="Search Options"
                  [options]="simpleOptions"
                  [search]="true"
                  (selectionChange)="onSelectionChange($event)"
                >
                </ava-dropdown>
              </div>
            </ng-container>

            <ng-container *ngSwitchCase="'Options with Icons'">
              <div class="row g-3">
                <div class="col-12 col-sm-auto">
                  <ava-dropdown
                    dropdownTitle="Network Options"
                    [options]="iconOptions"
                    (selectionChange)="onSelectionChange($event)"
                  >
                  </ava-dropdown>
                </div>
                <div class="col-12 col-sm-auto">
                  <ava-dropdown
                    dropdownTitle="With Search"
                    [options]="iconOptions"
                    [search]="true"
                    (selectionChange)="onSelectionChange($event)"
                  >
                  </ava-dropdown>
                </div>
              </div>
            </ng-container>

            <ng-container *ngSwitchCase="'Custom Dropdown Icons'">
              <div
                style="
                  display: flex;
                  flex-wrap: wrap;
                  gap: 1rem;
                  align-items: flex-start;
                "
              >
                <ava-dropdown
                  dropdownTitle="Default Chevron"
                  [options]="simpleOptions"
                  (selectionChange)="onSelectionChange($event)"
                >
                </ava-dropdown>
                <ava-dropdown
                  dropdownTitle="Circle Check"
                  [dropdownIcon]="'circle-check'"
                  [options]="simpleOptions"
                  (selectionChange)="onSelectionChange($event)"
                >
                </ava-dropdown>
                <ava-dropdown
                  dropdownTitle="Settings Icon"
                  [dropdownIcon]="'settings'"
                  [options]="simpleOptions"
                  (selectionChange)="onSelectionChange($event)"
                >
                </ava-dropdown>
                <ava-dropdown
                  dropdownTitle="Plus Icon"
                  [dropdownIcon]="'plus'"
                  [options]="simpleOptions"
                  (selectionChange)="onSelectionChange($event)"
                >
                </ava-dropdown>
              </div>
            </ng-container>

            <ng-container *ngSwitchCase="'Custom Option Icons'">
              <div
                style="
                  display: flex;
                  flex-wrap: wrap;
                  gap: 1rem;
                  align-items: flex-start;
                "
              >
                <ava-dropdown
                  dropdownTitle="Navigation Options"
                  [options]="customIconOptions"
                  (selectionChange)="onSelectionChange($event)"
                >
                </ava-dropdown>
                <ava-dropdown
                  dropdownTitle="System Options"
                  [options]="iconOptions"
                  (selectionChange)="onSelectionChange($event)"
                >
                </ava-dropdown>
              </div>
            </ng-container>

            <ng-container *ngSwitchCase="'Multi-select'">
              <div class="row g-3">
                <div class="col-12 col-sm-auto">
                  <!-- Control buttons for managing selections -->
                  <div class="mb-3">
                    <button class="btn btn-sm btn-outline-danger me-2" (click)="removeOneOption()">Remove Last</button>
                    <button class="btn btn-sm btn-outline-warning me-2" (click)="removeFirstOption()">Remove First</button>
                    <button class="btn btn-sm btn-outline-info me-2" (click)="removeOptionByName('Feature B')">Remove Feature B</button>
                    <button class="btn btn-sm btn-danger me-2" (click)="clearAllOptions()">Clear All</button>
                    <button class="btn btn-sm btn-success" (click)="resetToDefault()">Reset</button>
                  </div>

                  <!-- Display current selections -->
                  <div class="mb-3 p-2 bg-light rounded">
                    <small class="text-muted">Current selections: </small>
                    <span *ngIf="selectedmulti.length === 0" class="text-muted fst-italic">None</span>
                    <span *ngIf="selectedmulti.length > 0" class="fw-bold">{{ selectedmulti.join(', ') }}</span>
                  </div>

                  <ava-dropdown
                    dropdownTitle="Multi-select Features"
                    [options]="checkboxOptions"
                    [checkboxOptions]="['Feature A', 'Feature B', 'Feature C']"
                    [selectedValues]="selectedmulti"
                    [width]="'250px'"
                    (selectionChange)="onSelectionChange($event)"
                  >
                  </ava-dropdown>
                </div>
                <div class="col-12 col-sm-auto">
                  <ava-dropdown
                    dropdownTitle="With Search"
                    [options]="checkboxOptions"
                    [checkboxOptions]="['Feature A', 'Feature B', 'Feature C']"
                    [search]="true"
                    (selectionChange)="onSelectionChange($event)"
                  >
                  </ava-dropdown>
                </div>
              </div>
            </ng-container>

            <ng-container *ngSwitchCase="'Single-select'">
              <div class="row g-3">
                <div class="col-12 col-sm-auto">
                  <ava-dropdown
                    dropdownTitle="Single Select"
                    [options]="checkboxOptions"
                    [checkboxOptions]="['Feature A', 'Feature B', 'Feature C']"
                    [singleSelect]="true"
                    (selectionChange)="onSelectionChange($event)"
                  >
                  </ava-dropdown>
                </div>
              </div>
            </ng-container>
            <ng-container *ngSwitchCase="'Disabled Dropdown'">
              <div class="disabled-demo">
                <div
                  style="
                    display: flex;
                    flex-wrap: wrap;
                    gap: 1rem;
                    align-items: flex-start;
                  "
                >
                  <ava-dropdown
                    dropdownTitle="Disabled Dropdown"
                    [options]="simpleOptions"
                    [disabled]="true"
                    (selectionChange)="onSelectionChange($event)"
                  >
                  </ava-dropdown>
                  <ava-dropdown
                    dropdownTitle="Normal Dropdown"
                    [options]="simpleOptions"
                    (selectionChange)="onSelectionChange($event)"
                  >
                  </ava-dropdown>
                </div>
              </div>
            </ng-container>

            <ng-container *ngSwitchCase="'Disabled Options'">
              <div class="disabled-options-demo">
                <div
                  style="
                    display: grid;
                    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
                    gap: 1rem;
                  "
                >
                  <ava-dropdown
                    dropdownTitle="Mixed Options"
                    [options]="disabledOptions"
                    (selectionChange)="onSelectionChange($event)"
                  >
                  </ava-dropdown>
                  <ava-dropdown
                    dropdownTitle="Categories with Disabled Sub-options"
                    [options]="[
                      { name: 'Categories', value: 'categories' },
                      { name: 'Services', value: 'services' }
                    ]"
                    [suboptions]="disabledSuboptions"
                    (selectionChange)="onSelectionChange($event)"
                  >
                  </ava-dropdown>
                  <ava-dropdown
                    dropdownTitle="Disabled Checkboxes"
                    [options]="disabledCheckboxOptions"
                    [checkboxOptions]="[
                      'Feature A',
                      'Feature B (Disabled)',
                      'Feature C',
                      'Feature D (Disabled)'
                    ]"
                    (selectionChange)="onSelectionChange($event)"
                  >
                  </ava-dropdown>
                </div>
              </div>
            </ng-container>

            <ng-container *ngSwitchCase="'Label and Required Fields'">
              <div class="label-required-demo">
                <div
                  style="
                    display: grid;
                    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
                    gap: 2rem;
                  "
                >
                  <div>
                    <ava-dropdown
                      label="Category"
                      dropdownTitle="Select a category"
                      [options]="basicOptions"
                      (selectionChange)="onSelectionChange($event)"
                    >
                    </ava-dropdown>
                  </div>

                  <div>
                    <ava-dropdown
                      label="Required Category"
                      [required]="true"
                      dropdownTitle="Select a category"
                      [options]="basicOptions"
                      (selectionChange)="onSelectionChange($event)"
                    >
                    </ava-dropdown>
                  </div>

                  <div>
                    <ava-dropdown
                      [required]="true"
                      [search]="true"
                      dropdownTitle="Search and select"
                      [options]="basicOptions"
                      [suboptions]="basicSuboptions"
                      (selectionChange)="onSelectionChange($event)"
                    >
                    </ava-dropdown>
                  </div>

                  <div>
                    <h4>Custom Error Message</h4>
                    <ava-dropdown
                      label="Payment Method"
                      dropdownTitle="Select payment method"
                      [options]="basicOptions"
                      [required]="true"
                      error="Please select a payment method"
                      (selectionChange)="onSelectionChange($event)"
                    >
                    </ava-dropdown>
                  </div>
                </div>
              </div>
            </ng-container>
          </ng-container>
        </div>

        <div class="code-block" *ngIf="section.showCode">
          <div class="code-content">
            <pre><code [innerText]="getDropdownCode(section.title.toLowerCase())"></code></pre>
          </div>
          <button
            class="copy-button"
            (click)="copyCode(section.title.toLowerCase())"
          >
            <!-- <awe-icons iconName="awe_copy"></awe-icons> -->
          </button>
        </div>
      </div>
    </section>
  </div>

  <!-- Reactive Forms Demo -->
  <div style="max-width: 800px; margin: 40px auto">
    <h2>Reactive Forms Integration</h2>
    <form
      [formGroup]="testForm"
      (ngSubmit)="onSubmit()"
      style="display: grid; gap: 1rem"
    >
      <ava-dropdown
        formControlName="category"
        dropdownTitle="Simple Options"
        [error]="getFieldError('category')"
        [required]="true"
        label="Category"
        [options]="simpleOptions"
        (selectionChange)="onSelectionChange($event)"
      >
      </ava-dropdown>
      <ava-button
        label="Submit Form"
        variant="primary"
        type="submit"
      ></ava-button>
    </form>

    <div style="margin-top: 1rem">
      <strong>Form Status:</strong> {{ testForm.valid ? "Valid" : "Invalid" }}
      <br />
      <strong>Form Value:</strong> {{ testForm.value | json }}
    </div>
  </div>
  <section class="doc-section api-reference">
    <h2>API Reference</h2>
    <table class="api-table">
      <thead>
        <tr>
          <th>Property</th>
          <th>Type</th>
          <th>Default</th>
          <th>Description</th>
        </tr>
      </thead>
      <tbody>
        <tr *ngFor="let prop of apiProps">
          <td>
            <code>{{ prop.name }}</code>
          </td>
          <td>
            <code>{{ prop.type }}</code>
          </td>
          <td>
            <code>{{ prop.default }}</code>
          </td>
          <td>{{ prop.description }}</td>
        </tr>
      </tbody>
    </table>
  </section>

  <section class="doc-section events-section">
    <h2>Events</h2>
    <table class="api-table">
      <thead>
        <tr>
          <th>Event</th>
          <th>Type</th>
          <th>Description</th>
        </tr>
      </thead>
      <tbody>
        <tr *ngFor="let event of events">
          <td>
            <code>{{ event.name }}</code>
          </td>
          <td>
            <code>{{ event.type }}</code>
          </td>
          <td>{{ event.description }}</td>
        </tr>
      </tbody>
    </table>
  </section>
</div>
