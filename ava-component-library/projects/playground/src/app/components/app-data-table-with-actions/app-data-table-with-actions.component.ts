import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import {
  DataTableWithActionsComponent,
  DataTableConfig,
  DataTableEvent,
} from '../../../../../play-comp-library/src/lib/composite-components/data-table-with-actions/data-table-with-actions.component';
import { CardComponent } from '../../../../../play-comp-library/src/lib/components/card/card.component';
import { ButtonComponent } from '../../../../../play-comp-library/src/lib/components/button/button.component';
import { IconComponent } from '../../../../../play-comp-library/src/lib/components/icon/icon.component';

@Component({
  selector: 'ava-data-table-with-actions-demo',
  standalone: true,
  imports: [
    CommonModule,
    DataTableWithActionsComponent,
    CardComponent,
    ButtonComponent,
    IconComponent,
  ],
  templateUrl: './app-data-table-with-actions.component.html',
  styleUrl: './app-data-table-with-actions.component.scss',
})
export class AppDataTableWithActionsComponent {
  dataTableConfig: DataTableConfig = {
    columns: [
      {
        id: 'id',
        label: 'ID',
        sortable: true,
        width: '80px',
        align: 'center',
      },
      {
        id: 'name',
        label: 'Name',
        sortable: true,
        width: '200px',
      },
      {
        id: 'email',
        label: 'Email',
        sortable: true,
        width: '250px',
      },
      {
        id: 'role',
        label: 'Role',
        sortable: true,
        width: '120px',
      },
      {
        id: 'status',
        label: 'Status',
        sortable: true,
        width: '100px',
        align: 'center',
        render: (value: unknown) => {
          const status = String(value);
          return `<span class="status-badge status-${status.toLowerCase()}">${status}</span>`;
        },
      },
      {
        id: 'lastLogin',
        label: 'Last Login',
        sortable: true,
        width: '150px',
        align: 'center',
      },
    ],
    actions: [
      {
        id: 'view',
        label: 'View',
        icon: 'eye',
        variant: 'info',
        size: 'small',
      },
      {
        id: 'edit',
        label: 'Edit',
        icon: 'edit',
        variant: 'primary',
        size: 'small',
      },
      {
        id: 'delete',
        label: 'Delete',
        icon: 'trash',
        variant: 'danger',
        size: 'small',
        showForRow: (row: Record<string, unknown>) =>
          String(row['status']) !== 'Active',
      },
    ],
    bulkActions: [
      {
        id: 'export',
        label: 'Export Selected',
        icon: 'download',
        variant: 'secondary',
      },
      {
        id: 'activate',
        label: 'Activate',
        icon: 'check-circle',
        variant: 'success',
      },
      {
        id: 'deactivate',
        label: 'Deactivate',
        icon: 'x-circle',
        variant: 'warning',
      },
    ],
    showSearch: true,
    showPagination: true,
    showBulkActions: true,
    showSelectAll: true,
    itemsPerPage: 5,
    searchPlaceholder: 'Search users...',
    emptyMessage: 'No users found',
    loadingMessage: 'Loading users...',
  };

  sampleData: Record<string, unknown>[] = [
    {
      id: 1,
      name: 'John Doe',
      email: '<EMAIL>',
      role: 'Admin',
      status: 'Active',
      lastLogin: '2024-01-15 10:30',
    },
    {
      id: 2,
      name: 'Jane Smith',
      email: '<EMAIL>',
      role: 'User',
      status: 'Active',
      lastLogin: '2024-01-14 15:45',
    },
    {
      id: 3,
      name: 'Bob Johnson',
      email: '<EMAIL>',
      role: 'Manager',
      status: 'Inactive',
      lastLogin: '2024-01-10 09:20',
    },
    {
      id: 4,
      name: 'Alice Brown',
      email: '<EMAIL>',
      role: 'User',
      status: 'Active',
      lastLogin: '2024-01-16 14:15',
    },
    {
      id: 5,
      name: 'Charlie Wilson',
      email: '<EMAIL>',
      role: 'Admin',
      status: 'Pending',
      lastLogin: '2024-01-12 11:30',
    },
    {
      id: 6,
      name: 'Diana Davis',
      email: '<EMAIL>',
      role: 'User',
      status: 'Active',
      lastLogin: '2024-01-16 16:45',
    },
    {
      id: 7,
      name: 'Edward Miller',
      email: '<EMAIL>',
      role: 'Manager',
      status: 'Inactive',
      lastLogin: '2024-01-08 13:20',
    },
    {
      id: 8,
      name: 'Fiona Garcia',
      email: '<EMAIL>',
      role: 'User',
      status: 'Active',
      lastLogin: '2024-01-16 08:30',
    },
  ];

  loading = false;
  disabled = false;
  totalItems = 8;
  currentPage = 1;
  lastEvent: DataTableEvent | null = null;

  onActionClick(event: { actionId: string; row: Record<string, unknown> }) {
    console.log('Row action clicked:', event);
    alert(`${event.actionId} action for user: ${event.row['name']}`);
  }

  onBulkActionClick(event: {
    actionId: string;
    selectedRows: Record<string, unknown>[];
  }) {
    console.log('Bulk action clicked:', event);
    alert(
      `${event.actionId} action for ${event.selectedRows.length} selected users`
    );
  }

  onSelectionChange(selectedRows: Record<string, unknown>[]) {
    console.log('Selection changed:', selectedRows);
  }

  onPageChange(page: number) {
    console.log('Page changed:', page);
    this.currentPage = page;
  }

  onSearchChange(searchTerm: string) {
    console.log('Search changed:', searchTerm);
  }

  onSortChange(sortData: { column: string; direction: 'asc' | 'desc' }) {
    console.log('Sort changed:', sortData);
  }

  onDataTableEvent(event: DataTableEvent) {
    this.lastEvent = event;
    console.log('Data table event:', event);
  }

  toggleLoading() {
    this.loading = !this.loading;
  }

  toggleDisabled() {
    this.disabled = !this.disabled;
  }

  refreshData() {
    this.loading = true;
    setTimeout(() => {
      this.loading = false;
      console.log('Data refreshed');
    }, 2000);
  }
}
