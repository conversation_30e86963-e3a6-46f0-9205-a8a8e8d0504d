<div class="data-table-with-actions-demo">
  <div class="demo-header">
    <h2>Data Table with Actions Component</h2>
    <p>
      This component demonstrates a comprehensive data table with search,
      sorting, pagination, row actions, and bulk operations.
    </p>
  </div>

  <div class="demo-controls-card">
    <ava-card>
      <div header>
        <h3>Demo Controls</h3>
      </div>
      <div content>
        <div class="controls-grid">
          <ava-button
            (click)="toggleLoading()"
            [variant]="loading ? 'warning' : 'primary'"
            size="medium"
            [label]="loading ? 'Stop Loading' : 'Start Loading'"
            [iconName]="loading ? 'pause' : 'play'"
            [iconSize]="16"
          >
          </ava-button>
          <ava-button
            (click)="toggleDisabled()"
            [variant]="disabled ? 'success' : 'secondary'"
            size="medium"
            [label]="disabled ? 'Enable' : 'Disable'"
            [iconName]="disabled ? 'unlock' : 'lock'"
            [iconSize]="16"
          >
          </ava-button>
          <ava-button
            (click)="refreshData()"
            variant="info"
            size="medium"
            label="Refresh Data"
            iconName="refresh-cw"
            [iconSize]="16"
          >
          </ava-button>
        </div>
      </div>
    </ava-card>
  </div>

  <div class="demo-section">
    <ava-card>
      <div header>
        <h3>User Management Table</h3>
        <p>Interactive data table with search, sort, and bulk operations</p>
      </div>
      <div content>
        <ava-data-table-with-actions
          [config]="dataTableConfig"
          [data]="sampleData"
          [loading]="loading"
          [disabled]="disabled"
          [totalItems]="totalItems"
          [currentPage]="currentPage"
          (actionClick)="onActionClick($event)"
          (bulkActionClick)="onBulkActionClick($event)"
          (selectionChange)="onSelectionChange($event)"
          (pageChange)="onPageChange($event)"
          (searchChange)="onSearchChange($event)"
          (sortChange)="onSortChange($event)"
          (dataTableEvent)="onDataTableEvent($event)"
        >
        </ava-data-table-with-actions>
      </div>
    </ava-card>
  </div>

  <div class="event-log-card" *ngIf="lastEvent">
    <ava-card>
      <div header>
        <h3>Event Log</h3>
        <p>Real-time events from the data table</p>
      </div>
      <div content>
        <div class="event-content">
          <pre>{{ lastEvent | json }}</pre>
        </div>
      </div>
    </ava-card>
  </div>

  <div class="features-card">
    <ava-card>
      <div header>
        <h3>Features Demonstrated</h3>
        <p>Comprehensive list of capabilities</p>
      </div>
      <div content>
        <div class="features-grid">
          <div class="feature-item">
            <ava-icon
              iconName="search"
              [iconSize]="20"
              iconColor="#007bff"
            ></ava-icon>
            <div class="feature-content">
              <strong>Search:</strong> Global search across all columns
            </div>
          </div>
          <div class="feature-item">
            <ava-icon
              iconName="arrow-up-down"
              [iconSize]="20"
              iconColor="#28a745"
            ></ava-icon>
            <div class="feature-content">
              <strong>Sorting:</strong> Click column headers to sort data
            </div>
          </div>
          <div class="feature-item">
            <ava-icon
              iconName="check-square"
              [iconSize]="20"
              iconColor="#ffc107"
            ></ava-icon>
            <div class="feature-content">
              <strong>Row Selection:</strong> Select individual rows or all rows
            </div>
          </div>
          <div class="feature-item">
            <ava-icon
              iconName="mouse-pointer-2"
              [iconSize]="20"
              iconColor="#dc3545"
            ></ava-icon>
            <div class="feature-content">
              <strong>Row Actions:</strong> View, edit, delete actions per row
            </div>
          </div>
          <div class="feature-item">
            <ava-icon
              iconName="layers"
              [iconSize]="20"
              iconColor="#6c757d"
            ></ava-icon>
            <div class="feature-content">
              <strong>Bulk Actions:</strong> Export, activate, deactivate
              selected rows
            </div>
          </div>
          <div class="feature-item">
            <ava-icon
              iconName="chevrons-left"
              [iconSize]="20"
              iconColor="#17a2b8"
            ></ava-icon>
            <div class="feature-content">
              <strong>Pagination:</strong> Navigate through large datasets
            </div>
          </div>
          <div class="feature-item">
            <ava-icon
              iconName="loader"
              [iconSize]="20"
              iconColor="#fd7e14"
            ></ava-icon>
            <div class="feature-content">
              <strong>Loading States:</strong> Visual feedback during operations
            </div>
          </div>
          <div class="feature-item">
            <ava-icon
              iconName="lock"
              [iconSize]="20"
              iconColor="#6f42c1"
            ></ava-icon>
            <div class="feature-content">
              <strong>Disabled States:</strong> Prevent interactions when needed
            </div>
          </div>
          <div class="feature-item">
            <ava-icon
              iconName="eye"
              [iconSize]="20"
              iconColor="#e83e8c"
            ></ava-icon>
            <div class="feature-content">
              <strong>Conditional Actions:</strong> Show/hide actions based on
              row data
            </div>
          </div>
          <div class="feature-item">
            <ava-icon
              iconName="smartphone"
              [iconSize]="20"
              iconColor="#20c997"
            ></ava-icon>
            <div class="feature-content">
              <strong>Responsive Design:</strong> Adapts to different screen
              sizes
            </div>
          </div>
        </div>
      </div>
    </ava-card>
  </div>
</div>
