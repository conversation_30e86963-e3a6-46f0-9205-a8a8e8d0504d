import { Component, ViewEncapsulation } from '@angular/core';
import { CommonModule } from '@angular/common';
import {
  DrawerComponent,
  ButtonComponent,
  CheckboxComponent,
  AvaTextboxComponent,
  BadgesComponent,
  AvatarsComponent
} from '../../../../../play-comp-library/src/public-api';
import { AgentDetailsDemoComponent } from './demo/agent-details-demo.component';

interface DrawerDocSection {
  title: string;
  description: string;
  showCode: boolean;
}

@Component({
  selector: 'app-drawer',
  standalone: true,
  imports: [
    CommonModule,
    DrawerComponent,
    ButtonComponent,
    CheckboxComponent,
    AvaTextboxComponent,
    BadgesComponent,
    AvatarsComponent,
    AgentDetailsDemoComponent
  ],
  templateUrl: './app-drawer.component.html',
  styleUrls: ['./app-drawer.component.scss'],
  encapsulation: ViewEncapsulation.None
})
export class AppDrawerComponent {
  // Documentation Sections
  sections: DrawerDocSection[] = [

    {
      title: 'Positions',
      description: 'Drawers can slide in from any side: left, right, top, or bottom.',
      showCode: false
    },
    {
      title: 'Sizes',
      description: 'Available sizes: small, medium, large, extra-large, and full.',
      showCode: false
    },

    {
      title: 'Smooth Animation',
      description: 'Elegant slide animation with smooth easing for professional, polished transitions.',
      showCode: false
    },
    {
      title: 'Behavior Properties',
      description: 'Control drawer behavior with input properties: overlay, persistence, animations, and more.',
      showCode: false
    },
    {
      title: 'Trigger Examples',
      description: 'Drawers can be triggered by any element: buttons, links, icons, images, text, etc.',
      showCode: false
    },
    {
      title: 'Real-world Examples',
      description: 'Practical examples inspired by modern UI patterns.',
      showCode: false
    }
  ];

  // Drawer States
  
  // Position drawers
  leftDrawerOpen = false;
  rightDrawerOpen = false;
  topDrawerOpen = false;
  bottomDrawerOpen = false;

  // Size drawers
  smallDrawerOpen = false;
  mediumDrawerOpen = false;
  largeDrawerOpen = false;
  extraLargeDrawerOpen = false;
  fullDrawerOpen = false;

  // Smooth animation drawers
  smoothRightDrawerOpen = false;
  smoothLeftDrawerOpen = false;
  smoothTopDrawerOpen = false;
  smoothBottomDrawerOpen = false;

  // No animation drawer
  noAnimationDrawerOpen = false;

  // Behavior property examples
  showOverlayTrueDrawer = false;
  showOverlayFalseDrawer = false;
  closeOnOverlayTrueDrawer = false;
  closeOnOverlayFalseDrawer = false;
  closeOnEscapeTrueDrawer = false;
  closeOnEscapeFalseDrawer = false;
  showCloseButtonTrueDrawer = false;
  showCloseButtonFalseDrawer = false;
  persistentTrueDrawer = false;
  persistentFalseDrawer = false;
  animateTrueDrawer = false;
  animateFalseDrawer = false;

  // Additional variables that HTML is expecting
  overlayTrueDrawerOpen = false;
  overlayFalseDrawerOpen = false;
  overlayClickTrueDrawerOpen = false;
  overlayClickFalseDrawerOpen = false;
  escapeTrueDrawerOpen = false;
  escapeFalseDrawerOpen = false;
  closeButtonTrueDrawerOpen = false;
  closeButtonFalseDrawerOpen = false;
  persistentFalseDrawerOpen = false;
  persistentTrueDrawerOpen = false;
  animateTrueDrawerOpen = false;
  animateFalseDrawerOpen = false;

  // Trigger examples
  buttonTriggerDrawerOpen = false;
  linkTriggerDrawerOpen = false;
  iconTriggerDrawerOpen = false;
  imageTriggerDrawerOpen = false;
  textTriggerDrawerOpen = false;
  cardTriggerDrawerOpen = false;

  // Real-world example drawers
  userProfileDrawerOpen = false;
  settingsDrawerOpen = false;
  notificationsDrawerOpen = false;
  candidateDetailsDrawerOpen = false;

  // Sample data for examples
  notifications = [
    { id: 1, title: 'New message from John', time: '2 min ago', read: false },
    { id: 2, title: 'Project deadline reminder', time: '1 hour ago', read: false },
    { id: 3, title: 'Weekly report ready', time: '3 hours ago', read: true },
    { id: 4, title: 'Team meeting in 30 minutes', time: '5 hours ago', read: true }
  ];

  candidateData = {
    name: 'Sarah Johnson',
    position: 'Senior Frontend Developer',
    experience: '5+ years',
    location: 'San Francisco, CA',
    email: '<EMAIL>',
    phone: '+****************',
    skills: ['React', 'TypeScript', 'Angular', 'Vue.js', 'Node.js'],
    status: 'Under Review',
    appliedDate: '2024-01-15',
    rating: 4.5
  };

  /**
   * Toggle code visibility for a section
   */
  toggleCode(section: DrawerDocSection): void {
    section.showCode = !section.showCode;
  }

  /**
   * Get example code for a section
   */
  getExampleCode(section: string): string {
    return `<!-- Example code for ${section} -->
<ava-drawer [isOpen]="drawerOpen" title="${section} Example">
  <p>Content for ${section}</p>
</ava-drawer>`;
  }

  /**
   * Handle drawer events
   */
  onDrawerOpened(): void {
    console.log('Drawer opened');
  }

  onDrawerClosed(): void {
    console.log('Drawer closed');
  }

  onOverlayClick(): void {
    console.log('Overlay clicked');
  }

  onEscapePressed(): void {
    console.log('Escape key pressed');
  }

  /**
   * Mark notification as read
   */
  markAsRead(notification: any): void {
    notification.read = true;
  }

  /**
   * Clear all notifications
   */
  clearAllNotifications(): void {
    this.notifications = [];
  }

  /**
   * Get unread notifications count
   */
  get unreadNotificationsCount(): number {
    return this.notifications.filter(n => !n.read).length;
  }

  /**
   * Get notifications subtitle
   */
  get notificationsSubtitle(): string {
    return `You have ${this.unreadNotificationsCount} unread notifications`;
  }

  // Agent Demo Drawer
  agentDemoDrawerOpen = false;

  /**
   * Handle tag click in agent demo
   */
  onTagClick(tagName: string): void {
    console.log(`Agent tag clicked: ${tagName}`);
    // You can add more functionality here like filtering, navigation, etc.
  }

  /**
   * Handle stat navigation in agent demo
   */
  onStatNavigation(statType: string, direction: 'prev' | 'next'): void {
    console.log(`Stat navigation: ${statType} ${direction}`);
    // You can add functionality to cycle through different categories, developers, etc.
  }

  /**
   * Handle agent action button click
   */
  onAgentAction(): void {
    console.log('Agent action button clicked');
    // You can add functionality like "Use Agent", "Add to Favorites", etc.
  }
}
