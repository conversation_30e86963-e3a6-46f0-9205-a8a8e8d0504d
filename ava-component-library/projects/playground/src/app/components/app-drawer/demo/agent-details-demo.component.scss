/* ===================================================================
   DEMO SECTION STYLES
   =================================================================== */
.demo-section {
  background: var(--color-background-primary, #ffffff);
  padding: 2rem;
  border-radius: var(--global-radius-lg, 0.75rem);
  border: 2px solid var(--color-border-default, #e2e8f0);
  margin-bottom: 2rem;

  h4 {
    margin: 0 0 1rem 0;
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--color-text-primary, #1a202c);
  }

  p {
    margin: 0 0 1.5rem 0;
    color: var(--color-text-secondary, #64748b);
    line-height: 1.6;
  }

  .demo-button {
    background: var(--color-brand-primary, #e91e63);
    color: white;
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: var(--global-radius-md, 0.5rem);
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;

    &:hover {
      background: var(--color-brand-primary-hover, #d81b60);
      transform: translateY(-1px);
    }
  }
}

/* ===================================================================
   AGENT DETAILS DRAWER STYLES
   =================================================================== */
.agent-details {
  display: flex;
  flex-direction: column;
  gap: 2rem;
  padding: 0.5rem;
  height: 100%;
}

/* Header Section */
.agent-header {
  .agent-title {
    font-size: 2rem;
    font-weight: 700;
    color: var(--color-brand-primary, #e91e63);
    margin: 0 0 1rem 0;
    line-height: 1.2;
  }

  .agent-description {
    font-size: 1rem;
    color: var(--color-text-secondary, #64748b);
    line-height: 1.6;
    margin: 0;
  }
}

/* Tags Section */
.agent-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 0.75rem;

  .demo-tag {
    display: inline-flex;
    align-items: center;
    padding: 0.5rem 1rem;
    border-radius: var(--global-radius-full, 9999px);
    font-size: 0.875rem;
    font-weight: 500;
    background: var(--color-surface-secondary, #f1f5f9);
    color: var(--color-text-secondary, #64748b);
    border: 1px solid var(--color-border-subtle, #e2e8f0);
    transition: all 0.2s ease;

    &.primary {
      background: var(--color-brand-primary, #e91e63);
      color: white;
      border-color: var(--color-brand-primary, #e91e63);
    }

    &.outlined {
      background: transparent;
      border-color: var(--color-border-default, #d1d5db);

      &:hover {
        background: var(--color-surface-hover, #f8fafc);
      }
    }
  }
}

/* Stats Grid */
.agent-stats {
  .stats-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 1.5rem;
    padding: 1.5rem 0;
    border-bottom: 1px solid var(--color-border-subtle, #f1f5f9);
  }

  .stat-column {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
  }

  .stat-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    
    .stat-label {
      font-size: 0.875rem;
      color: var(--color-text-secondary, #64748b);
      font-weight: 500;
    }

    .nav-arrows {
      display: flex;
      gap: 0.25rem;
      
      .nav-arrow {
        background: none;
        border: none;
        color: var(--color-text-secondary, #64748b);
        cursor: pointer;
        padding: 0.25rem;
        border-radius: var(--global-radius-sm, 0.25rem);
        font-size: 1rem;
        
        &:hover {
          background: var(--color-surface-hover, #f8fafc);
        }
      }
    }

    .stat-icon {
      font-size: 1rem;
      color: var(--color-text-secondary, #64748b);
    }
  }

  .stat-value {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--color-text-primary, #1a202c);
    
    &.highlight {
      color: var(--color-brand-primary, #e91e63);
      font-weight: 700;
    }
  }

  .stat-sublabel {
    font-size: 0.875rem;
    color: var(--color-text-secondary, #64748b);
  }
}

/* Content Section */
.agent-content {
  flex: 1;
  
  .content-section {
    background: var(--color-surface-secondary, #f8fafc);
    padding: 1.5rem;
    border-radius: var(--global-radius-lg, 0.75rem);
    
    .section-title {
      font-size: 1.25rem;
      font-weight: 600;
      color: var(--color-text-primary, #1a202c);
      margin: 0 0 1rem 0;
    }

    .section-text {
      font-size: 0.9375rem;
      color: var(--color-text-secondary, #64748b);
      line-height: 1.6;
      margin: 0;
    }
  }
}

/* Action Buttons */
.agent-actions {
  margin-top: auto;
  padding-top: 1rem;

  .action-button {
    width: 100%;
    background: linear-gradient(135deg, #e91e63 0%, #ad1457 100%);
    color: white;
    border: none;
    padding: 1rem 2rem;
    border-radius: var(--global-radius-md, 0.5rem);
    font-weight: 600;
    font-size: 1rem;
    cursor: pointer;
    transition: all 0.2s ease;

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 8px 25px rgba(233, 30, 99, 0.3);
    }
  }
}

.close-actions {
  margin-top: 1rem;

  .close-button {
    background: var(--color-surface-secondary, #f1f5f9);
    color: var(--color-text-secondary, #64748b);
    border: 1px solid var(--color-border-default, #e2e8f0);
    padding: 0.5rem 1rem;
    border-radius: var(--global-radius-sm, 0.25rem);
    cursor: pointer;
    transition: all 0.2s ease;

    &:hover {
      background: var(--color-surface-hover, #e2e8f0);
    }
  }
}

/* Hidden state */
.hidden {
  display: none;
}

/* ===================================================================
   RESPONSIVE DESIGN
   =================================================================== */
@media (max-width: 768px) {
  .agent-stats .stats-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem;
  }
  
  .agent-header .agent-title {
    font-size: 1.75rem;
  }
}

@media (max-width: 480px) {
  .agent-stats .stats-grid {
    grid-template-columns: 1fr;
  }
  
  .agent-details {
    gap: 1.5rem;
  }
}
