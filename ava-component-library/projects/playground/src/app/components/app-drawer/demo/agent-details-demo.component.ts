import { Component, Output, EventEmitter } from '@angular/core';
import { CommonModule } from '@angular/common';

@Component({
  selector: 'app-agent-details-demo',
  standalone: true,
  imports: [
    CommonModule
  ],
  templateUrl: './agent-details-demo.component.html',
  styleUrls: ['./agent-details-demo.component.scss']
})
export class AgentDetailsDemoComponent {
  @Output() openAgentDrawer = new EventEmitter<void>();

  isDrawerOpen = false;

  // Agent data
  agentData = {
    title: 'Heading',
    description: 'Effortlessly convert Ruby code to Spring Boot with optimised migration',
    tags: [
      { label: '#1 in Agents', color: 'primary' as const, variant: 'filled' as const },
      { label: 'Code Migration', color: 'default' as const, variant: 'outlined' as const },
      { label: 'Development', color: 'default' as const, variant: 'outlined' as const },
      { label: 'Backend', color: 'default' as const, variant: 'outlined' as const }
    ],
    stats: {
      category: 'Type',
      developedBy: 'Name',
      relevancy: '9.5/10',
      rating: '4.5',
      relevancyScore: 'Score',
      ratingOutOf: 'Out of 5'
    },
    description_long: `A agent that converts Ruby code to Spring Boot can be highly beneficial for organisation's migrating from Ruby on Rails to Java Spring Boot. However, the effectiveness depends on several factors, including the complexity of the application, language differences, and the capabilities of the conversion agent.`
  };

  openDrawer() {
    this.openAgentDrawer.emit();
  }

  closeDrawer() {
    this.isDrawerOpen = false;
  }
}
