import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import {
  DrawerComponent,
  ButtonComponent,
} from '../../../../../../../play-comp-library/src/public-api';

@Component({
  selector: 'ava-resizable-demo',
  standalone: true,
  imports: [CommonModule, DrawerComponent, ButtonComponent],
  templateUrl: './resizable-demo.component.html',
  styleUrl: './resizable-demo.component.scss',
})
export class ResizableDemoComponent {
  // Resizable drawer examples
  resizableDrawerOpen = false;
  customSizeDrawerOpen = false;
  constrainedDrawerOpen = false;
  multiPositionDrawerOpen = false;

  openDrawer(type: string): void {
    switch (type) {
      case 'resizable':
        this.resizableDrawerOpen = true;
        break;
      case 'custom-size':
        this.customSizeDrawerOpen = true;
        break;
      case 'constrained':
        this.constrainedDrawerOpen = true;
        break;
      case 'multi-position':
        this.multiPositionDrawerOpen = true;
        break;
    }
  }

  closeDrawer(type: string): void {
    switch (type) {
      case 'resizable':
        this.resizableDrawerOpen = false;
        break;
      case 'custom-size':
        this.customSizeDrawerOpen = false;
        break;
      case 'constrained':
        this.constrainedDrawerOpen = false;
        break;
      case 'multi-position':
        this.multiPositionDrawerOpen = false;
        break;
    }
  }
}
