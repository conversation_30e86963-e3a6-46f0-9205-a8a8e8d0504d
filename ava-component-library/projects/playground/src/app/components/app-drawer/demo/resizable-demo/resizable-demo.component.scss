.resizable-demo {
  padding: 2rem;
  max-width: 1200px;
  margin: 0 auto;

  .demo-header {
    text-align: center;
    margin-bottom: 2rem;

    h3 {
      color: #333;
      margin-bottom: 0.5rem;
      font-size: 1.5rem;
    }

    p {
      color: #666;
      font-size: 1rem;
    }
  }

  .demo-content {
    .button-group {
      display: flex;
      gap: 1rem;
      justify-content: center;
      flex-wrap: wrap;
      margin-bottom: 2rem;
    }

    .content-section {
      h4 {
        color: #333;
        margin-bottom: 1rem;
        font-size: 1.25rem;
      }

      p {
        color: #555;
        margin-bottom: 1rem;
        line-height: 1.6;

        code {
          background: #f8f9fa;
          padding: 0.125rem 0.25rem;
          border-radius: 2px;
          font-family: monospace;
          color: #d63384;
        }
      }

      .resize-info,
      .size-info,
      .constraint-info,
      .position-info {
        background: #e8f5e8;
        padding: 1rem;
        border-radius: 4px;
        border-left: 4px solid #28a745;
        margin-bottom: 1.5rem;

        h5 {
          color: #155724;
          margin-bottom: 0.5rem;
          font-size: 1.1rem;
        }

        ul {
          margin: 0;
          padding-left: 1.5rem;

          li {
            color: #155724;
            margin-bottom: 0.25rem;
            line-height: 1.4;
          }
        }
      }

      .content-block {
        margin-bottom: 1.5rem;
        padding: 1rem;
        background: #f8f9fa;
        border-radius: 4px;
        border-left: 4px solid #007bff;

        h5 {
          color: #0056b3;
          margin-bottom: 0.5rem;
          font-size: 1.1rem;
        }

        p {
          color: #495057;
          margin-bottom: 0.75rem;
          line-height: 1.5;
        }

        ul {
          margin-top: 0.5rem;
          padding-left: 1.5rem;

          li {
            color: #495057;
            margin-bottom: 0.25rem;
            line-height: 1.4;
          }
        }
      }
    }
  }
}
