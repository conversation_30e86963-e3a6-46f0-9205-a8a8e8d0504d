<div class="popup-demo-container">
  <h1>Popup Component Demos</h1>
  <p class="demo-intro">
    Explore different aspects of the Popup component with interactive demos.
    Each demo showcases specific features and usage patterns.
  </p>
  <div class="demo-grid">
    <a class="demo-card" routerLink="/popup/basic-usage">
      <div class="demo-card-header">
        <h3>Basic Usage</h3>
        <span class="demo-badge">Fundamental</span>
      </div>
      <p class="demo-description">Show a simple popup with a message.</p>
      <div class="demo-preview"><span>🔔</span></div>
    </a>
    <a class="demo-card" routerLink="/popup/triggers">
      <div class="demo-card-header">
        <h3>Trigger Types</h3>
        <span class="demo-badge">Interaction</span>
      </div>
      <p class="demo-description">Show popup with manual trigger.</p>
      <div class="demo-preview"><span>🖱️</span></div>
    </a>
    <a class="demo-card" routerLink="/popup/positioning">
      <div class="demo-card-header">
        <h3>Positioning</h3>
        <span class="demo-badge">Layout</span>
      </div>
      <p class="demo-description">Show popup in different positions.</p>
      <div class="demo-preview"><span>📍</span></div>
    </a>
    <a class="demo-card" routerLink="/popup/animation">
      <div class="demo-card-header">
        <h3>Animation</h3>
        <span class="demo-badge">Motion</span>
      </div>
      <p class="demo-description">Show popup (no animation supported).</p>
      <div class="demo-preview"><span>✨</span></div>
    </a>
    <a class="demo-card" routerLink="/popup/custom-content">
      <div class="demo-card-header">
        <h3>Custom Content</h3>
        <span class="demo-badge">Content</span>
      </div>
      <p class="demo-description">Show popup with a form and list.</p>
      <div class="demo-preview"><span>📝</span></div>
    </a>
    <a class="demo-card" routerLink="/popup/accessibility">
      <div class="demo-card-header">
        <h3>Accessibility</h3>
        <span class="demo-badge">A11y</span>
      </div>
      <p class="demo-description">
        Show accessible popup with ARIA and keyboard support.
      </p>
      <div class="demo-preview"><span>♿️</span></div>
    </a>
  </div>
  <div class="code-preview-section">
    <h2>Quick Start</h2>
    <div class="code-preview">
      <pre><code>{{ quickStartCode }}</code></pre>
    </div>
  </div>
</div>

<!-- Existing documentation content below -->

<!-- Documentation Sections -->
<section *ngFor="let section of sections; let i = index" class="doc-section">
  <div class="section-header" (click)="toggleCodeVisibility(i, $event)">
    <h2>{{ section.title }}</h2>
    <div class="description-container">
      <p>{{ section.description }}</p>
      <div class="code-toggle">
        <span *ngIf="!section.showCode">View Code</span>
        <span *ngIf="section.showCode">Hide Code</span>
        <!--<awe-icons [iconName]="section.showCode ? 'awe_arrow_back_up' : 'awe_arrow_back_down'"
            iconColor="action"></awe-icons>-->
      </div>
    </div>
  </div>

  <div class="code-example" [class.expanded]="section.showCode">
    <div class="example-preview">
      <!-- Live popup preview based on type -->
      <ng-container [ngSwitch]="section.popupType">
        <div *ngSwitchCase="'info'">
          <ava-button
            label="Show Info Popup"
            variant="primary"
            (click)="showInfoPopup = true"
          >
          </ava-button>
        </div>
        <!-- Info -->
        <ava-popup
          *ngSwitchCase="'info'"
          messageAlignment="center"
          [show]="showInfoPopup"
          title="Message Sent Successfully!"
          message="Thank you for contacting us."
          [showHeaderIcon]="true"
          headerIconName="circle-check"
          iconColor="green"
          [showClose]="true"
          (closed)="showInfoPopup = false"
        >
        </ava-popup>

        <div *ngSwitchCase="'warning'">
          <ava-button
            label="Show Warning Popup"
            variant="primary"
            (click)="showWarningPopup = true"
          >
          </ava-button>
        </div>

        <!-- Warning -->
        <ava-popup
          *ngSwitchCase="'warning'"
          [show]="showWarningPopup"
          [title]="''"
          [showTitle]="false"
          [showHeaderIcon]="false"
          [showInlineMessage]="true"
          [inlineIconName]="'info'"
          [inlineIconSize]="48"
          [inlineIconColor]="'#007bff'"
          [inlineMessage]="'Heads up!'"
          [message]="
            'Deleting this item will remove it permanently from your records. This action is not reversible.'
          "
          [showClose]="true"
          [showCancel]="true"
          [cancelButtonLabel]="'Okay'"
          [cancelButtonVariant]="'primary'"
          [popupWidth]="'400px'"
          (closed)="showWarningPopup = false"
        >
        </ava-popup>

        <div *ngSwitchCase="'delete'">
          <ava-button
            label="Show Delete Popup"
            variant="primary"
            (click)="showDeletePopup = true"
          >
          </ava-button>
        </div>

        <!-- Delete -->
        <ava-popup
          *ngSwitchCase="'delete'"
          [show]="showDeletePopup"
          [title]="'Delete This Item?'"
          [showTitle]="true"
          [showHeaderIcon]="true"
          [headerIconName]="'trash'"
          [iconSize]="48"
          [iconColor]="'#dc3545'"
          [message]="'Are you sure you want to delete this item?'"
          [showClose]="true"
          [showCancel]="true"
          [showConfirm]="true"
          [cancelButtonLabel]="'Cancel'"
          [confirmButtonLabel]="'Delete'"
          [confirmButtonBackground]="'#dc3545'"
          [confirmButtonVariant]="'primary'"
          [popupWidth]="'400px'"
          (closed)="showDeletePopup = false"
        >
        </ava-popup>

        <!-- Feedback -->
        <div *ngSwitchCase="'feedback'">
          <ava-button
            label="Show Feedback Popup"
            variant="primary"
            (click)="openSendBackPopup()"
          >
          </ava-button>
          <ava-confirmation-popup
            *ngSwitchCase="'feedback'"
            [show]="showSendBackPopup"
            title="We Value Your Feedback"
            message="Please share your feedback below. Your input helps us make meaningful improvements."
            confirmationLabel="Send Back"
            (closed)="showSendBackPopup = false"
          >
          </ava-confirmation-popup>
        </div>

        <div *ngSwitchCase="'contact'">
          <ava-button
            label="Show Contact Popup"
            variant="primary"
            (click)="showContactForm = true"
          >
          </ava-button>
        </div>

        <div class="contact-form">
          <ava-popup
            title="Contact Form"
            message="Thank you for contacting us."
            [show]="showContactForm"
            [popupWidth]="'1000px'"
            [showHeaderIcon]="false"
            [showClose]="true"
            [showConfirm]="false"
            [showCancel]="false"
            role="dialog"
            aria-modal="true"
            aria-labelledby="popup-title"
            aria-describedby="popup-message"
            (closed)="showContactForm = false"
          >
            <form
              [formGroup]="demoForm"
              (ngSubmit)="onSubmit()"
              style="display: grid; gap: 1rem"
            >
              <ava-textbox
                label="Username"
                class="left"
                placeholder="Enter username"
                formControlName="username"
                [error]="getFieldError('username')"
                [required]="true"
              >
                <ava-icon
                  slot="icon-start"
                  iconName="user"
                  [iconColor]="'purple'"
                ></ava-icon>
              </ava-textbox>

              <ava-textbox
                label="Email"
                class="left"
                type="email"
                placeholder="<EMAIL>"
                formControlName="email"
                [error]="getFieldError('email')"
                [required]="true"
              >
                <ava-icon
                  slot="icon-start"
                  iconName="mail"
                  [iconColor]="'purple'"
                ></ava-icon>
              </ava-textbox>

              <ava-textbox
                label="Phone"
                class="left"
                type="tel"
                placeholder="+****************"
                formControlName="phone"
              >
                <ava-icon
                  slot="icon-start"
                  iconName="phone"
                  [iconColor]="'purple'"
                ></ava-icon>
              </ava-textbox>

              <ava-textbox
                label="Website"
                class="left"
                placeholder="your-site"
                formControlName="website"
              >
                <span slot="prefix">https://</span>
                <span slot="suffix">.com</span>
              </ava-textbox>

              <ava-button
                label="Submit Form"
                variant="primary"
                buttonSize="lg"
                type="submit"
              ></ava-button>
            </form>

            <div style="margin-top: 1rem">
              <strong>Form Status:</strong>
              {{ demoForm.valid ? "Valid" : "Invalid" }}
              <br />
              <strong>Form Value:</strong> {{ demoForm.value | json }}
            </div>
          </ava-popup>
        </div>

        <!-- Popup Positions Preview -->
        <div *ngSwitchCase="'positions'" class="popup-positions-demo">
          <div class="position-preview-grid">
            <div *ngFor="let pos of popupPositions" class="position-button">
              <ava-button
                [label]="'Show ' + pos"
                variant="primary"
                (click)="openPositionPopup[pos] = true"
              >
              </ava-button>

              <ava-popup
                [show]="openPositionPopup[pos]"
                [position]="pos"
                title="Message Sent Successfully!
"
                message="Thank you for contacting us."
                [popupWidth]="'350px'"
                [showHeaderIcon]="true"
                [showClose]="true"
                (closed)="openPositionPopup[pos] = false"
              >
              </ava-popup>
            </div>
          </div>
        </div>
      </ng-container>
    </div>

    <!-- Toggle Code -->
    <div class="code-block" *ngIf="section.showCode">
      <pre><code [innerText]="getExampleCode(section.popupType)"></code></pre>
      <button class="copy-button" (click)="copyCode(section.popupType)">
        <!--<awe-icons iconName="awe_copy"></awe-icons>-->
      </button>
    </div>
  </div>
</section>
