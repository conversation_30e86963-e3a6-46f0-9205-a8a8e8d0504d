.demo-container {
  min-height: 100vh;
  padding: 2rem;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.demo-header {
  text-align: center;
  margin-bottom: 3rem;

  h1 {
    font-size: 2.5rem;
    font-weight: 700;
    color: #1e293b;
    margin-bottom: 1rem;
  }

  p {
    font-size: 1.2rem;
    color: #64748b;
    max-width: 600px;
  }
}

.demo-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 2rem;
  width: 100%;
  max-width: 1200px;
}

.nav-bar-showcase {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  max-width: 800px !important;
  padding: 2rem;

  ava-nav-bar {
    // Ensure the nav-bar is perfectly centered
    display: flex;
    justify-content: center;
  }
}

.demo-info {
  text-align: center;
  padding: 1.5rem;

  p {
    margin: 0.5rem 0;
    color: #475569;

    &:first-child {
      font-weight: 600;
      color: #1e293b;
    }
  }
}

// Responsive adjustments
@media (max-width: 768px) {
  .demo-container {
    padding: 1rem;
  }

  .demo-header h1 {
    font-size: 2rem;
  }

  .nav-bar-showcase {
    padding: 1rem;
  }
}
