import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ConfirmationPopupComponent } from '../../../../../play-comp-library/src/lib/composite-components/confirmation-popup/confirmation-popup.component';

@Component({
  selector: 'ava-confirmation-popup-demo',
  standalone: true,
  imports: [CommonModule, ConfirmationPopupComponent],
  templateUrl: './app-confirmation-popup.component.html',
  styleUrl: './app-confirmation-popup.component.scss',
})
export class AppConfirmationPopupComponent {
  showPopup = false;
  popupData = {
    title: 'Confirm Action',
    message:
      'Are you sure you want to delete this item? This action cannot be undone.',
    confirmText: 'Delete',
    cancelText: 'Cancel',
    type: 'danger',
  };

  showConfirmation() {
    this.showPopup = true;
  }

  onConfirm() {
    console.log('Action confirmed!');
    this.showPopup = false;
  }

  onCancel() {
    console.log('Action cancelled!');
    this.showPopup = false;
  }
}
